import os
import sys
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
import matplotlib.pyplot as plt

class GeorefConfigCreator:
    def __init__(self, root):
        self.root = root
        self.root.title("Georeferencing Config Creator")
        self.root.geometry("1200x800")

        # Initialize variables
        self.image_path = None
        self.shapefile_path = None
        self.marked_points = []  # List of (pixel_x, pixel_y, lat, lon)
        self.scale_factor = 1.0

        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create control panel
        control_frame = ttk.LabelFrame(self.main_frame, text="Controls")
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # Image selection
        ttk.Label(control_frame, text="Image:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.image_var = tk.StringVar()
        ttk.Entry(control_frame, textvariable=self.image_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(control_frame, text="Browse...", command=self.browse_image).grid(row=0, column=2, padx=5, pady=5)

        # Shapefile selection (for reference)
        ttk.Label(control_frame, text="Reference Shapefile:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.shapefile_var = tk.StringVar()
        ttk.Entry(control_frame, textvariable=self.shapefile_var, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(control_frame, text="Browse...", command=self.browse_shapefile).grid(row=1, column=2, padx=5, pady=5)

        # Action buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)

        ttk.Button(button_frame, text="Mark Point", command=self.start_marking).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear Points", command=self.clear_points).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Config", command=self.save_config).pack(side=tk.LEFT, padx=5)

        # Zoom controls
        zoom_frame = ttk.Frame(button_frame)
        zoom_frame.pack(side=tk.LEFT, padx=20)
        ttk.Button(zoom_frame, text="Zoom In", command=self.zoom_in).pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="Zoom Out", command=self.zoom_out).pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="Reset Zoom", command=self.reset_zoom).pack(side=tk.LEFT, padx=5)

        # Create canvas for image display
        canvas_frame = ttk.Frame(self.main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg="gray")

        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)

        self.canvas.config(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Bind events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<Motion>", self.on_canvas_move)
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)  # Windows
        self.canvas.bind("<Button-4>", self.on_mousewheel)    # Linux scroll up
        self.canvas.bind("<Button-5>", self.on_mousewheel)    # Linux scroll down

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Point coordinates entry
        coord_frame = ttk.LabelFrame(self.main_frame, text="Point Coordinates")
        coord_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

        ttk.Label(coord_frame, text="Latitude:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.lat_var = tk.StringVar()
        ttk.Entry(coord_frame, textvariable=self.lat_var, width=15).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(coord_frame, text="Longitude:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.lon_var = tk.StringVar()
        ttk.Entry(coord_frame, textvariable=self.lon_var, width=15).grid(row=0, column=3, padx=5, pady=5)

        ttk.Button(coord_frame, text="Add Point", command=self.add_current_point).grid(row=0, column=4, padx=5, pady=5)

        # Points list
        points_frame = ttk.LabelFrame(self.main_frame, text="Marked Points")
        points_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

        # Create treeview for points
        self.points_tree = ttk.Treeview(points_frame, columns=("Pixel X", "Pixel Y", "Latitude", "Longitude"), show="headings")
        self.points_tree.heading("Pixel X", text="Pixel X")
        self.points_tree.heading("Pixel Y", text="Pixel Y")
        self.points_tree.heading("Latitude", text="Latitude")
        self.points_tree.heading("Longitude", text="Longitude")

        self.points_tree.column("Pixel X", width=100)
        self.points_tree.column("Pixel Y", width=100)
        self.points_tree.column("Latitude", width=100)
        self.points_tree.column("Longitude", width=100)

        self.points_tree.pack(fill=tk.X, padx=5, pady=5)

        # Variables for marking
        self.marking_point = False
        self.temp_point = None

        # Set default values if provided
        if len(sys.argv) > 1:
            self.image_var.set(sys.argv[1])
            self.load_image(sys.argv[1])

        if len(sys.argv) > 2:
            self.shapefile_var.set(sys.argv[2])
            self.load_shapefile(sys.argv[2])

    def browse_image(self):
        """Browse for an image file."""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.tif;*.tiff"), ("All files", "*.*")]
        )
        if file_path:
            self.image_var.set(file_path)
            self.load_image(file_path)

    def browse_shapefile(self):
        """Browse for a shapefile."""
        file_path = filedialog.askopenfilename(
            title="Select Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.shapefile_var.set(file_path)
            self.load_shapefile(file_path)

    def load_image(self, path):
        """Load an image from the specified path."""
        try:
            self.image_path = path
            self.original_image = Image.open(path)
            self.update_image()
            self.status_var.set(f"Loaded image: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.status_var.set(f"Error loading image: {str(e)}")

    def load_shapefile(self, path):
        """Load a shapefile from the specified path."""
        try:
            self.shapefile_path = path
            self.gdf = gpd.read_file(path)
            self.status_var.set(f"Loaded shapefile: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load shapefile: {str(e)}")
            self.status_var.set(f"Error loading shapefile: {str(e)}")

    def update_image(self):
        """Update the displayed image based on the current scale factor."""
        if hasattr(self, 'original_image'):
            # Resize image based on scale factor
            width = int(self.original_image.width * self.scale_factor)
            height = int(self.original_image.height * self.scale_factor)
            resized_image = self.original_image.resize((width, height), Image.LANCZOS)

            # Convert to PhotoImage
            self.tk_image = ImageTk.PhotoImage(resized_image)

            # Update canvas
            self.canvas.delete("all")
            self.canvas_image = self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)
            self.canvas.config(scrollregion=self.canvas.bbox(tk.ALL))

            # Redraw all points
            self.redraw_points()

    def redraw_points(self):
        """Redraw all marked points on the canvas."""
        for i, (px, py, lat, lon) in enumerate(self.marked_points):
            # Convert to canvas coordinates
            canvas_x = px * self.scale_factor
            canvas_y = py * self.scale_factor

            # Draw point
            point_id = self.canvas.create_oval(
                canvas_x - 5, canvas_y - 5, canvas_x + 5, canvas_y + 5,
                fill="red", outline="white", tags=f"point_{i}"
            )

            # Draw label
            label_id = self.canvas.create_text(
                canvas_x, canvas_y - 15,
                text=f"{i+1}: ({lat:.6f}, {lon:.6f})",
                fill="white", font=("Arial", 8), tags=f"label_{i}"
            )

    def start_marking(self):
        """Start marking a point on the image."""
        if not hasattr(self, 'original_image'):
            messagebox.showwarning("Warning", "Please load an image first.")
            return

        self.marking_point = True
        self.status_var.set("Click on the image to mark a point.")

    def on_canvas_click(self, event):
        """Handle canvas click event."""
        if not hasattr(self, 'original_image'):
            messagebox.showwarning("Warning", "Please load an image first.")
            return

        # Get canvas coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)

        if self.marking_point:
            # Store temporary point
            self.temp_point = (original_x, original_y)

            # Update status
            self.status_var.set(f"Point marked at pixel ({original_x}, {original_y}). Enter coordinates and click 'Add Point'.")

            # End marking mode
            self.marking_point = False

    def add_current_point(self):
        """Add the current point with the entered coordinates."""
        if self.temp_point is None:
            messagebox.showwarning("Warning", "Please mark a point on the image first.")
            return

        try:
            # Get coordinates from entry fields
            lat = float(self.lat_var.get())
            lon = float(self.lon_var.get())

            # Add point to list
            self.marked_points.append((self.temp_point[0], self.temp_point[1], lat, lon))

            # Add to treeview
            self.points_tree.insert("", "end", values=(self.temp_point[0], self.temp_point[1], lat, lon))

            # Update display
            self.update_image()

            # Clear temporary point and entry fields
            self.temp_point = None
            self.lat_var.set("")
            self.lon_var.set("")

            # Update status
            self.status_var.set(f"Point added: Pixel ({self.temp_point[0]}, {self.temp_point[1]}), Geo ({lat}, {lon})")
        except ValueError:
            messagebox.showerror("Error", "Invalid coordinates. Please enter valid numbers.")

    def clear_points(self):
        """Clear all marked points."""
        self.marked_points = []
        self.temp_point = None
        self.lat_var.set("")
        self.lon_var.set("")

        # Clear treeview
        for item in self.points_tree.get_children():
            self.points_tree.delete(item)

        # Update display
        self.update_image()

        # Update status
        self.status_var.set("All points cleared.")

    def save_config(self):
        """Save the georeferencing configuration to a JSON file."""
        if not self.marked_points:
            messagebox.showwarning("Warning", "No points to save.")
            return

        if not hasattr(self, 'original_image'):
            messagebox.showwarning("Warning", "No image loaded.")
            return

        try:
            # Create data structure
            data = {
                "image_path": self.image_path,
                "image_width": self.original_image.width,
                "image_height": self.original_image.height,
                "points": []
            }

            for px, py, lat, lon in self.marked_points:
                data["points"].append({
                    "pixel_x": px,
                    "pixel_y": py,
                    "latitude": lat,
                    "longitude": lon
                })

            # Save to file
            file_path = filedialog.asksaveasfilename(
                title="Save Georeferencing Config",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialfile="map_georef_config.json"
            )

            if file_path:
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=4)
                self.status_var.set(f"Saved configuration to {file_path}")
                messagebox.showinfo("Success", f"Configuration saved to {file_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def on_canvas_move(self, event):
        """Handle mouse movement over the canvas."""
        if hasattr(self, 'original_image'):
            # Get canvas coordinates
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)

            # Convert to original image coordinates
            original_x = int(canvas_x / self.scale_factor)
            original_y = int(canvas_y / self.scale_factor)

            # Update status bar with pixel coordinates
            self.status_var.set(f"Pixel: ({original_x}, {original_y})")

    def on_mousewheel(self, event):
        """Handle mouse wheel for zooming."""
        if event.num == 4 or event.delta > 0:  # Scroll up
            self.zoom_in()
        elif event.num == 5 or event.delta < 0:  # Scroll down
            self.zoom_out()

    def zoom_in(self):
        """Zoom in on the image."""
        self.scale_factor *= 1.1
        self.update_image()

    def zoom_out(self):
        """Zoom out on the image."""
        self.scale_factor /= 1.1
        self.update_image()

    def reset_zoom(self):
        """Reset zoom to original scale."""
        self.scale_factor = 1.0
        self.update_image()

if __name__ == "__main__":
    import sys

    root = tk.Tk()
    app = GeorefConfigCreator(root)
    root.mainloop()
