# Color-Based Image Masking Tool

A tool for creating masks from images by selecting specific colors.

## Features

- **Color Picking**: Select colors directly from the image or use a color picker
- **Adjustable Threshold**: Control the color matching sensitivity
- **Multiple Colors**: Select and combine multiple colors for complex masks
- **Real-time Preview**: See the mask and masked result as you work
- **Export Options**: Save the mask or masked image in various formats

## Requirements

- Python 3.7 or higher
- Required Python packages (install via `pip install -r requirements.txt`):
  - numpy
  - opencv-python
  - matplotlib
  - pillow

## Installation

1. Ensure Python is installed on your system
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

1. Run the application by double-clicking `launch_masking_tool.bat` or running:
   ```
   python color_masking_tool.py
   ```

2. Load an image:
   - The default image path is pre-configured
   - Click "Browse..." to select a different image
   - Click "Load Image" to load the image

3. Select colors to mask:
   - Click "Pick Color from Image" and then click on the image to select a color
   - Or use "Choose Color from Palette" to select a color from a color picker
   - Adjust the "Color Threshold" slider to control the range of color matching
   - Add multiple colors as needed

4. View the results:
   - Switch between tabs to view the original image, mask, and masked result
   - Choose between "Combined Mask" (OR of all color masks) or "Individual Masks" view

5. Export your results:
   - Click "Save Mask Image" to save just the black and white mask
   - Click "Save Masked Original Image" to save the original image with non-selected areas removed

## Tips

- Start with a higher threshold and gradually reduce it to fine-tune your mask
- For complex images, select multiple similar colors to ensure complete coverage
- Use the individual masks view to see how each color contributes to the final mask
- You can remove colors from the list if they're not needed 