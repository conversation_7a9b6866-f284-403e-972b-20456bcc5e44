#!/usr/bin/env python3
"""
Simple test script for DWG to Shapefile conversion
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from main import DWGToShapefileConverter
    print("✓ Successfully imported DWGToShapefileConverter")
    
    # Test file path
    dwg_file = r"C:\Users\<USER>\Downloads\drive-download-20250527T030512Z-1-001\HGU 8 UTM.DWG"
    
    print(f"Testing with file: {dwg_file}")
    print(f"File exists: {Path(dwg_file).exists()}")
    
    if Path(dwg_file).exists():
        print("\n" + "="*50)
        print("Starting conversion...")
        print("="*50)
        
        converter = DWGToShapefileConverter()
        created_files = converter.convert_dwg_to_shapefile(dwg_file)
        
        if created_files:
            print(f"\n✓ Success! Created {len(created_files)} shapefile(s):")
            for file_path in created_files:
                print(f"  - {file_path}")
        else:
            print("\n✗ Conversion failed. Check the log for details.")
    else:
        print(f"\n✗ File not found: {dwg_file}")
        print("Please check the file path and try again.")
        
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()

print("\nTest completed.")
