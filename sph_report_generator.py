#!/usr/bin/env python3
"""
SPH (Stands Per Hectare) Report Generator
Generates Excel reports from SPH polygon data with filtering and summarization
Author: Augment Code
Date: 2025-06-09
"""

import os
import sys
import pandas as pd
import geopandas as gpd
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import logging
from datetime import datetime
import numpy as np

class SPHReportGenerator:
    def __init__(self):
        self.setup_logging()
        self.default_path = r"D:\Gawean Rebinmas\Geo  Processing GIS Alat\report_SPH_unplanted"
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_filename = f"sph_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def select_sph_file(self, default_path=None):
        """Select SPH file using file dialog"""
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        # Set initial directory
        initial_dir = default_path if default_path and os.path.exists(default_path) else os.getcwd()
        
        # File types that might contain SPH data
        filetypes = [
            ("Shapefile", "*.shp"),
            ("Excel files", "*.xlsx;*.xls"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Pilih file SPH Polygon",
            initialdir=initial_dir,
            filetypes=filetypes
        )
        
        root.destroy()
        return filename
        
    def read_sph_data(self, file_path):
        """Read SPH data from various file formats"""
        self.logger.info(f"Reading SPH data from: {file_path}")
        
        file_ext = Path(file_path).suffix.lower()
        
        try:
            if file_ext == '.shp':
                # Read shapefile
                gdf = gpd.read_file(file_path)
                df = pd.DataFrame(gdf.drop(columns='geometry'))
            elif file_ext in ['.xlsx', '.xls']:
                # Read Excel file
                df = pd.read_excel(file_path)
            elif file_ext == '.csv':
                # Read CSV file
                df = pd.read_csv(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_ext}")
                
            self.logger.info(f"Successfully read {len(df)} records")
            self.logger.info(f"Columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error reading file: {str(e)}")
            raise
            
    def standardize_column_names(self, df):
        """Standardize column names to match expected format"""
        column_mapping = {
            # Try to map various column name variations
            'ID': 'Id',
            'id': 'Id',
            'DIVISI': 'Divisi',
            'divisi': 'Divisi',
            'SUB_DIVISI': 'SUB_DIVISI',
            'sub_divisi': 'SUB_DIVISI',
            'SUBDIVISI': 'SUB_DIVISI',
            'subdivisi': 'SUB_DIVISI',
            'BLOK': 'BLOK',
            'blok': 'BLOK',
            'Block': 'BLOK',
            'block': 'BLOK',
            'LUAS_AUTO': 'LUAS_AUTO',
            'luas_auto': 'LUAS_AUTO',
            'Luas_Auto': 'LUAS_AUTO',
            'JUMLAH_POH': 'Jumlah_Poh',
            'jumlah_poh': 'Jumlah_Poh',
            'Jumlah_Pohon': 'Jumlah_Poh',
            'jumlah_pohon': 'Jumlah_Poh',
            'HCV_CATEGO': 'HCV_Catego',
            'hcv_catego': 'HCV_Catego',
            'HCV_Category': 'HCV_Catego',
            'hcv_category': 'HCV_Catego',
            'ID_FEATURE': 'ID_Feature',
            'id_feature': 'ID_Feature',
            'IdFeature': 'ID_Feature',
            'LUAS_AUT_1': 'luas_aut_1',
            'luas_auto_1': 'luas_aut_1',
            'SPH': 'SPH',
            'sph': 'SPH',
            'CONTAINED': 'contained',
            'contained_': 'contained',
            'TOTAL_INCL': 'total_incl',
            'total_incl': 'total_incl',
            'Total_Incl': 'total_incl',
            'LUAS_NETTO': 'luas_netto',
            'luas_netto': 'luas_netto',
            'Luas_Netto': 'luas_netto'
        }
        
        # Rename columns
        df_renamed = df.rename(columns=column_mapping)
        
        # Log column mapping
        self.logger.info(f"Column mapping applied. Available columns: {list(df_renamed.columns)}")
        
        return df_renamed
        
    def filter_boundary_records(self, df):
        """Filter records for Boundary features with HCV_Catego = 0"""
        self.logger.info("Filtering for Boundary records with HCV_Catego = 0")
        
        # Ensure required columns exist
        required_columns = ['ID_Feature', 'HCV_Catego']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            self.logger.warning(f"Missing columns: {missing_columns}")
            # Try alternative column names
            if 'ID_Feature' not in df.columns:
                for col in df.columns:
                    if 'feature' in col.lower() or 'id' in col.lower():
                        df['ID_Feature'] = df[col]
                        self.logger.info(f"Using {col} as ID_Feature")
                        break
                        
            if 'HCV_Catego' not in df.columns:
                for col in df.columns:
                    if 'hcv' in col.lower() or 'categ' in col.lower():
                        df['HCV_Catego'] = df[col]
                        self.logger.info(f"Using {col} as HCV_Catego")
                        break
        
        # Filter for Boundary records
        try:
            boundary_mask = df['ID_Feature'].astype(str).str.contains('Boundary-', case=False, na=False)
            hcv_mask = df['HCV_Catego'] == 0
            
            filtered_df = df[boundary_mask & hcv_mask].copy()
            
            self.logger.info(f"Filtered {len(filtered_df)} Boundary records with HCV_Catego = 0 from {len(df)} total records")
            
            if len(filtered_df) == 0:
                self.logger.warning("No records found matching filter criteria")
                self.logger.info("Sample of ID_Feature values:")
                self.logger.info(df['ID_Feature'].head(10).tolist() if 'ID_Feature' in df.columns else "ID_Feature column not found")
                self.logger.info("Sample of HCV_Catego values:")
                self.logger.info(df['HCV_Catego'].unique().tolist() if 'HCV_Catego' in df.columns else "HCV_Catego column not found")
            
            return filtered_df
            
        except Exception as e:
            self.logger.error(f"Error filtering records: {str(e)}")
            self.logger.info("Available columns: " + str(list(df.columns)))
            raise
            
    def calculate_inclave_areas(self, df, filtered_df):
        """Calculate inclave areas for each boundary polygon"""
        self.logger.info("Calculating inclave areas...")
        
        inclave_areas = {}
        
        # Find inclave records
        try:
            inclave_mask = df['ID_Feature'].astype(str).str.contains('Inclave-', case=False, na=False)
            inclave_df = df[inclave_mask].copy()
            
            self.logger.info(f"Found {len(inclave_df)} inclave records")
            
            # Group inclave areas by the containing boundary
            for _, boundary_row in filtered_df.iterrows():
                boundary_id = boundary_row['ID_Feature']
                divisi = boundary_row['Divisi']
                sub_divisi = boundary_row['SUB_DIVISI']
                blok = boundary_row['BLOK']
                
                # Find related inclave records (same Divisi, Sub_Divisi, Blok)
                related_inclave = inclave_df[
                    (inclave_df['Divisi'] == divisi) &
                    (inclave_df['SUB_DIVISI'] == sub_divisi) &
                    (inclave_df['BLOK'] == blok)
                ]
                
                total_inclave_area = related_inclave['luas_aut_1'].sum() if 'luas_aut_1' in related_inclave.columns else 0
                if total_inclave_area == 0:
                    total_inclave_area = related_inclave['LUAS_AUTO'].sum() if 'LUAS_AUTO' in related_inclave.columns else 0
                
                key = (divisi, sub_divisi, blok)
                if key not in inclave_areas:
                    inclave_areas[key] = 0
                inclave_areas[key] += total_inclave_area
                
            self.logger.info(f"Calculated inclave areas for {len(inclave_areas)} boundary groups")
            
        except Exception as e:
            self.logger.warning(f"Error calculating inclave areas: {str(e)}")
            inclave_areas = {}
            
        return inclave_areas
        
    def generate_summary_report(self, df):
        """Generate hierarchical summary report with multiple levels"""
        self.logger.info("Generating hierarchical summary report...")
        
        # Standardize column names
        df = self.standardize_column_names(df)
        
        # Filter for boundary records
        boundary_df = self.filter_boundary_records(df)
        
        if len(boundary_df) == 0:
            raise ValueError("No boundary records found after filtering")
        
        # Calculate inclave areas
        inclave_areas = self.calculate_inclave_areas(df, boundary_df)
        
        # Group by Divisi, Sub Divisi, Blok
        grouping_columns = ['Divisi', 'SUB_DIVISI', 'BLOK']
        
        # Ensure grouping columns exist
        for col in grouping_columns:
            if col not in boundary_df.columns:
                self.logger.error(f"Required column {col} not found in data")
                self.logger.info(f"Available columns: {list(boundary_df.columns)}")
                raise ValueError(f"Required column {col} not found")
        
        # Clean numeric columns
        numeric_columns = ['LUAS_AUTO', 'Jumlah_Poh', 'SPH', 'luas_netto']
        for col in numeric_columns:
            if col in boundary_df.columns:
                boundary_df[col] = pd.to_numeric(boundary_df[col], errors='coerce')
        
        # 1. Detail by Block (finest level)
        detail_summary = boundary_df.groupby(grouping_columns).agg({
            'LUAS_AUTO': 'sum',
            'Jumlah_Poh': 'sum',
            'SPH': 'mean',
            'luas_netto': 'sum'
        }).reset_index()
        
        # Add inclave areas to detail
        detail_summary['Luas_Inclave'] = detail_summary.apply(
            lambda row: inclave_areas.get((row['Divisi'], row['SUB_DIVISI'], row['BLOK']), 0),
            axis=1
        )
        
        # Calculate actual luas netto if not available
        if 'luas_netto' not in boundary_df.columns or boundary_df['luas_netto'].isna().all():
            detail_summary['luas_netto'] = detail_summary['LUAS_AUTO'] - detail_summary['Luas_Inclave']
        
        # 2. Summary by Sub Division
        subdiv_summary = detail_summary.groupby(['Divisi', 'SUB_DIVISI']).agg({
            'LUAS_AUTO': 'sum',
            'Jumlah_Poh': 'sum',
            'SPH': 'mean',
            'luas_netto': 'sum',
            'Luas_Inclave': 'sum',
            'BLOK': 'count'  # Count number of blocks
        }).reset_index()
        subdiv_summary = subdiv_summary.rename(columns={'BLOK': 'Jumlah_Blok'})
        
        # 3. Summary by Division
        div_summary = subdiv_summary.groupby(['Divisi']).agg({
            'LUAS_AUTO': 'sum',
            'Jumlah_Poh': 'sum',
            'SPH': 'mean',
            'luas_netto': 'sum',
            'Luas_Inclave': 'sum',
            'SUB_DIVISI': 'count',  # Count number of sub divisions
            'Jumlah_Blok': 'sum'
        }).reset_index()
        div_summary = div_summary.rename(columns={'SUB_DIVISI': 'Jumlah_Sub_Divisi'})
        
        # 4. Grand Total Summary
        grand_total = {
            'Total_Divisi': len(div_summary),
            'Total_Sub_Divisi': div_summary['Jumlah_Sub_Divisi'].sum(),
            'Total_Blok': div_summary['Jumlah_Blok'].sum(),
            'Total_Luas_Auto_Ha': div_summary['LUAS_AUTO'].sum(),
            'Total_Luas_Inclave_Ha': div_summary['Luas_Inclave'].sum(),
            'Total_Luas_Netto_Ha': div_summary['luas_netto'].sum(),
            'Total_Jumlah_Pohon': div_summary['Jumlah_Poh'].sum(),
            'Rata_rata_SPH_Keseluruhan': div_summary['SPH'].mean()
        }
        
        # Round numeric columns for all summaries
        numeric_cols = ['LUAS_AUTO', 'Luas_Inclave', 'luas_netto', 'SPH']
        for summary_df in [detail_summary, subdiv_summary, div_summary]:
            for col in numeric_cols:
                if col in summary_df.columns:
                    summary_df[col] = summary_df[col].round(2)
        
        # Round grand total values
        for key, value in grand_total.items():
            if isinstance(value, (int, float)) and 'Ha' in key or 'SPH' in key:
                grand_total[key] = round(value, 2)
        
        # Sort all summaries
        detail_summary = detail_summary.sort_values(['Divisi', 'SUB_DIVISI', 'BLOK'])
        subdiv_summary = subdiv_summary.sort_values(['Divisi', 'SUB_DIVISI'])
        div_summary = div_summary.sort_values(['Divisi'])
        
        self.logger.info(f"Generated hierarchical summary:")
        self.logger.info(f"- Detail records: {len(detail_summary)}")
        self.logger.info(f"- Sub Division groups: {len(subdiv_summary)}")
        self.logger.info(f"- Division groups: {len(div_summary)}")
        
        return {
            'grand_total': grand_total,
            'division_summary': div_summary,
            'subdivision_summary': subdiv_summary,
            'detail_summary': detail_summary
        }
        
    def export_to_excel(self, summary_df, original_df, output_path=None):
        """Export summary and detailed data to Excel"""
        if output_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f"SPH_Report_Summary_{timestamp}.xlsx"
        
        self.logger.info(f"Exporting report to: {output_path}")
        
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Write summary report
                summary_df.to_excel(writer, sheet_name='Summary_Report', index=False)
                
                # Write filtered boundary data
                boundary_df = self.filter_boundary_records(self.standardize_column_names(original_df))
                if len(boundary_df) > 0:
                    boundary_df.to_excel(writer, sheet_name='Boundary_Data', index=False)
                
                # Write all original data for reference
                original_df.to_excel(writer, sheet_name='All_Data', index=False)
                
                # Format the summary sheet
                workbook = writer.book
                summary_sheet = writer.sheets['Summary_Report']
                
                # Auto-adjust column widths
                for column in summary_sheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    summary_sheet.column_dimensions[column_letter].width = adjusted_width
                
            self.logger.info(f"Report successfully exported to: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {str(e)}")
            raise
            
    def run_gui_mode(self):
        """Run the program with GUI interface"""
        root = tk.Tk()
        root.title("SPH Report Generator")
        root.geometry("600x400")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="SPH Report Generator", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # File selection
        ttk.Label(main_frame, text="File SPH:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.file_var = tk.StringVar(value=self.default_path)
        file_entry = ttk.Entry(main_frame, textvariable=self.file_var, width=50)
        file_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        ttk.Button(main_frame, text="Browse", command=self.browse_file).grid(row=1, column=2, padx=(5, 0))
        
        # Generate button
        generate_btn = ttk.Button(main_frame, text="Generate Report", command=self.generate_report_gui)
        generate_btn.grid(row=2, column=0, columnspan=3, pady=20)
        
        # Progress text
        self.progress_text = tk.Text(main_frame, height=15, width=70)
        self.progress_text.grid(row=3, column=0, columnspan=3, pady=10)
        
        # Scrollbar for text
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.progress_text.yview)
        scrollbar.grid(row=3, column=3, sticky=(tk.N, tk.S))
        self.progress_text.configure(yscrollcommand=scrollbar.set)
        
        # Configure grid weights
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        root.mainloop()
        
    def browse_file(self):
        """Browse for SPH file"""
        filename = self.select_sph_file(self.file_var.get())
        if filename:
            self.file_var.set(filename)
            
    def generate_report_gui(self):
        """Generate report from GUI"""
        try:
            file_path = self.file_var.get()
            if not file_path or not os.path.exists(file_path):
                messagebox.showerror("Error", "Please select a valid SPH file")
                return
                
            self.progress_text.delete(1.0, tk.END)
            self.progress_text.insert(tk.END, "Starting report generation...\n")
            self.progress_text.update()
            
            # Read data
            self.progress_text.insert(tk.END, f"Reading data from: {file_path}\n")
            self.progress_text.update()
            
            df = self.read_sph_data(file_path)
            
            # Generate summary
            self.progress_text.insert(tk.END, "Generating summary report...\n")
            self.progress_text.update()
            
            summary = self.generate_summary_report(df)
            
            # Export to Excel
            self.progress_text.insert(tk.END, "Exporting to Excel...\n")
            self.progress_text.update()
            
            output_file = self.export_to_excel(summary, df)
            
            self.progress_text.insert(tk.END, f"✓ Report generated successfully: {output_file}\n")
            self.progress_text.insert(tk.END, f"Summary contains {len(summary)} records\n")
            self.progress_text.update()
            
            messagebox.showinfo("Success", f"Report generated successfully!\n\nFile: {output_file}")
            
        except Exception as e:
            error_msg = f"Error generating report: {str(e)}"
            self.progress_text.insert(tk.END, f"✗ {error_msg}\n")
            self.progress_text.update()
            messagebox.showerror("Error", error_msg)
            
    def run_cli_mode(self, file_path=None):
        """Run the program in command line mode"""
        try:
            # Get file path
            if not file_path:
                file_path = self.select_sph_file(self.default_path)
                if not file_path:
                    self.logger.info("No file selected. Exiting.")
                    return
            
            # Read data
            df = self.read_sph_data(file_path)
            
            # Generate summary
            summary = self.generate_summary_report(df)
            
            # Export to Excel
            output_file = self.export_to_excel(summary, df)
            
            self.logger.info(f"✓ Report generated successfully: {output_file}")
            self.logger.info(f"Summary contains {len(summary)} records")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"✗ Error generating report: {str(e)}")
            raise

def main():
    """Main function"""
    generator = SPHReportGenerator()
    
    # Check if running with command line arguments
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        generator.run_cli_mode(file_path)
    else:
        # Ask user for mode
        root = tk.Tk()
        root.withdraw()
        
        choice = messagebox.askyesnocancel(
            "SPH Report Generator",
            "Pilih mode:\n\nYes = GUI Mode\nNo = CLI Mode\nCancel = Exit"
        )
        
        root.destroy()
        
        if choice is None:  # Cancel
            return
        elif choice:  # Yes - GUI mode
            generator.run_gui_mode()
        else:  # No - CLI mode
            generator.run_cli_mode()

if __name__ == "__main__":
    main()