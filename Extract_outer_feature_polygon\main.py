import geopandas as gpd
from shapely.ops import unary_union

# Path ke file shapefile
input_shapefile = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\All_Areal_tanam_Boundary.shp"
output_shapefile = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\Outer_Boundary.shp"

# Membaca shapefile
gdf = gpd.read_file(input_shapefile)

# Menggabungkan semua polygon menjadi satu (dissolve)
combined_polygon = unary_union(gdf.geometry)

# Membuat GeoDataFrame baru dengan batas luar saja
outer_boundary_gdf = gpd.GeoDataFrame(geometry=[combined_polygon], crs=gdf.crs)

# Menyimpan hasil ke shapefile baru
outer_boundary_gdf.to_file(output_shapefile)

print("Proses selesai! Batas luar telah disimpan ke:", output_shapefile)