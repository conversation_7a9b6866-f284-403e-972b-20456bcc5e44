# SPH Report Generator

Program untuk menghasilkan laporan Excel dari data polygon SPH (Stands Per Hectare) dengan filtering dan summarisasi berdasarkan Divisi, <PERSON> Divisi, dan <PERSON><PERSON>.

## Fitur Utama

- **Filter Otomatis**: <PERSON>ya memproses record dengan `ID_Feature` yang mengandung "Boundary-" dan `HCV_Catego = 0`
- **Kalkulasi Inclave**: Otomatis menghitung luas area inclave dari record yang mengandung "Inclave-"
- **Summary Report**: Menghasilkan laporan ringkasan per Divisi, Sub Divisi, dan <PERSON>lok
- **Multiple Format**: Mendukung Shapefile (.shp), Excel (.xlsx/.xls), dan <PERSON> (.csv)
- **GUI dan CLI**: Interface grafis dan command line
- **Export Excel**: Menghasilkan file Excel dengan multiple sheet (Summary, Boundary Data, All Data)

## Persyaratan Sistem

- Python 3.7 atau lebih tinggi
- Windows 10/11 (recommended)
- Minimum 4GB RAM
- 100MB disk space untuk dependencies

## Instalasi

### 1. Install Python
Download dan install Python dari [python.org](https://python.org/downloads/)

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run Program
**Menggunakan Batch Script (Recommended):**
```bash
run_sph_report.bat
```

**Menggunakan Python Langsung:**
```bash
python sph_report_generator.py
```

## Cara Penggunaan

### Mode GUI (Grafis)
1. Jalankan program
2. Pilih "Yes" untuk GUI Mode
3. Klik "Browse" untuk memilih file SPH
4. Klik "Generate Report"
5. File Excel akan dihasilkan otomatis

### Mode CLI (Command Line)
1. Jalankan program
2. Pilih "No" untuk CLI Mode
3. Pilih file SPH melalui dialog
4. Program akan otomatis memproses dan menghasilkan laporan

### Command Line dengan Parameter
```bash
python sph_report_generator.py "path/to/your/sph_file.shp"
```

## Format Data Input

Program mendukung file dengan struktur kolom berikut:

| Kolom | Deskripsi | Tipe |
|-------|-----------|------|
| Id | ID record | Integer |
| Divisi | Nama divisi | String |
| SUB_DIVISI | Nama sub divisi | String |
| BLOK | Nama blok | String |
| LUAS_AUTO | Luas otomatis (kotor) | Float |
| Jumlah_Poh | Jumlah pohon | Integer |
| HCV_Catego | Kategori HCV (0 = normal) | Integer |
| ID_Feature | ID fitur (Boundary-XX atau Inclave-XX) | String |
| luas_aut_1 | Luas area 1 | Float |
| SPH | Stands Per Hectare | Float |
| contained | Area yang terkandung | String |
| total_incl | Total inclusion | Float |
| luas_netto | Luas netto | Float |

## Output Report

Program menghasilkan file Excel dengan 3 sheet:

### 1. Summary_Report
Laporan ringkasan per Divisi, Sub Divisi, Blok dengan kolom:
- **Divisi**: Nama divisi
- **Sub_Divisi**: Nama sub divisi  
- **Blok**: Nama blok
- **Total_Luas_Auto_Ha**: Total luas auto (kotor) dalam hektare
- **Total_Luas_Inclave_Ha**: Total luas inclave dalam hektare
- **Total_Luas_Netto_Ha**: Total luas netto dalam hektare
- **Total_Jumlah_Pohon**: Total jumlah pohon
- **Rata_rata_SPH**: Rata-rata SPH (Stands Per Hectare)

### 2. Boundary_Data  
Data detail dari record Boundary yang difilter

### 3. All_Data
Semua data asli untuk referensi

## Algoritma Filtering

1. **Filter Boundary**: Hanya record dengan `ID_Feature` mengandung "Boundary-"
2. **Filter HCV**: Hanya record dengan `HCV_Catego = 0`
3. **Kalkulasi Inclave**: 
   - Cari record dengan `ID_Feature` mengandung "Inclave-"
   - Kelompokkan berdasarkan Divisi, Sub Divisi, Blok yang sama
   - Jumlahkan luas inclave per kelompok
4. **Summarisasi**: Kelompokkan data boundary per Divisi, Sub Divisi, Blok
5. **Agregasi**: 
   - Sum: Total luas, jumlah pohon
   - Mean: Rata-rata SPH

## Testing

Jalankan test dengan sample data:
```bash
python test_sph_report.py
```

Test akan membuat sample data dan menguji semua fungsi program.

## Troubleshooting

### Error: "No boundary records found"
- Periksa format kolom `ID_Feature` dan `HCV_Catego`
- Pastikan ada record dengan `ID_Feature` mengandung "Boundary-"
- Pastikan ada record dengan `HCV_Catego = 0`

### Error: "Required column not found"
- Periksa nama kolom sesuai dengan format yang diharapkan
- Program akan mencoba mapping otomatis untuk variasi nama kolom

### Error: "File format not supported"
- Pastikan file format adalah .shp, .xlsx, .xls, atau .csv
- Untuk shapefile, pastikan semua file pendukung (.dbf, .shx, .prj) ada

### Performance Issues
- Untuk file besar (>100MB), gunakan mode CLI
- Pastikan RAM minimal 4GB available
- Close aplikasi lain yang tidak perlu

## Log Files

Program akan membuat log file dengan format:
`sph_report_YYYYMMDD_HHMMSS.log`

Log berisi informasi detail proses, error, dan statistik.

## Contoh Data

Sample data sesuai format:
```
Id,Divisi,SUB_DIVISI,BLOK,LUAS_AUTO,Jumlah_Poh,HCV_Catego,ID_Feature,luas_aut_1,SPH,contained,total_incl,luas_netto
0,DIVISI ARE B 2,SUB DIVISI AIR BANGEK,P 98 / 03,52.03,6914,0,Boundary-60,52.03,133.89,,0,52.03
0,DIVISI ARE B 2,SUB DIVISI AIR BANGEK,P 98 / 02,73.01,9439,0,Boundary-61,73.05,137.94,"Inclave-12, Inclave-13",4.39,68.62
0,DIVISI ARE B 2,SUB DIVISI AIR BANGEK,P 98 / 02,73.01,,1,Inclave-12,0.69,,,
0,DIVISI ARE B 2,SUB DIVISI AIR BANGEK,P 98 / 02,73.01,,1,Inclave-13,0.66,,,
```

## Support

Untuk bantuan atau pertanyaan, silakan buat issue di repository ini atau hubungi developer.

---

**Author**: Augment Code  
**Date**: 2025-06-09  
**Version**: 1.0.0 