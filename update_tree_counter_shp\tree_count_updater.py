#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tree Count Updater

This utility updates tree counts in a boundary polygon shapefile based on
actual tree detection point files. It scans sub-division folders for detection
point files, counts the trees in each file, and updates the corresponding
boundary polygon with the accurate tree count and SPH (stems per hectare) values.

Author: AI Assistant
Date: 2025-05-13
"""

import os
import sys
import re
import glob
import geopandas as gpd
import pandas as pd
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('tree_count_updater.log')
    ]
)
logger = logging.getLogger(__name__)

class TreeCountUpdater:
    """Class to update tree counts in boundary polygon shapefile."""

    def __init__(self, boundary_file=None, root_dir=None, combined_point_file=None):
        """Initialize the TreeCountUpdater class.

        Args:
            boundary_file (str): Path to boundary polygon shapefile
            root_dir (str): Path to root directory containing sub-division folders
            combined_point_file (str): Path to combined detection point shapefile
        """
        self.boundary_file = boundary_file
        self.root_dir = root_dir
        self.combined_point_file = combined_point_file
        self.boundary_gdf = None
        self.combined_points_gdf = None
        self.detection_files = {}  # Map of block name to detection file path
        self.updates = []  # List of updates made
        self.errors = []  # List of errors encountered
        self.update_method = "spatial"  # Default to spatial analysis (can be "spatial", "name", or "hybrid")
        self.update_inclave = False  # Whether to update inclave polygons
        self.block_name_variants = {}  # Map of normalized block names to original block names
        self.use_combined_file = False  # Whether to use combined point file
        self.force_spatial_for_duplicates = True  # Whether to force spatial method for duplicate blocks

    def load_boundary_file(self):
        """Load boundary polygon shapefile.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Loading boundary file: {self.boundary_file}")
            self.boundary_gdf = gpd.read_file(self.boundary_file)
            logger.info(f"Loaded {len(self.boundary_gdf)} boundary polygons")
            return True
        except Exception as e:
            logger.error(f"Error loading boundary file: {str(e)}")
            self.errors.append(f"Error loading boundary file: {str(e)}")
            return False

    def normalize_block_name(self, block_name):
        """Normalize block name for fuzzy matching.

        Args:
            block_name (str): Block name to normalize

        Returns:
            str: Normalized block name
        """
        if not block_name:
            return ""

        # Convert to string if not already
        block_name = str(block_name).strip()

        # Remove common separators and whitespace
        normalized = block_name.replace(" ", "").replace("_", "").replace("/", "").replace("-", "").replace("\\", "")

        # Convert to uppercase for case-insensitive matching
        normalized = normalized.upper()

        return normalized

    def similar_block_name(self, name1, name2):
        """Check if two block names are similar.

        Args:
            name1 (str): First block name
            name2 (str): Second block name

        Returns:
            bool: True if names are similar, False otherwise
        """
        # Normalize both names
        norm1 = self.normalize_block_name(name1)
        norm2 = self.normalize_block_name(name2)

        # Check exact match after normalization
        if norm1 == norm2:
            return True

        # If one is contained in the other
        if norm1 in norm2 or norm2 in norm1:
            return True

        # Extract block number pattern (e.g., P0801 from different formats)
        import re
        pattern = r'[A-Za-z]+\s*\d+\s*[/\\-_]?\s*\d+'

        match1 = re.search(pattern, name1)
        match2 = re.search(pattern, name2)

        if match1 and match2:
            # Compare the normalized matched patterns
            if self.normalize_block_name(match1.group(0)) == self.normalize_block_name(match2.group(0)):
                return True

        return False

    def analyze_directory_structure(self):
        """Analyze directory structure to identify naming patterns.

        Returns:
            dict: Block name patterns found
        """
        logger.info("Analyzing directory structure to identify naming patterns")
        block_folders = []

        # Find all subdirectories that might contain block folders
        for root, dirs, _ in os.walk(self.root_dir):
            for dir_name in dirs:
                # Skip obvious non-block directories
                if "hasil" in dir_name.lower() or "output" in dir_name.lower() or "combined" in dir_name.lower():
                    continue

                # Look for patterns like P0801, P_08_01, P 08/01, etc.
                if re.search(r'[A-Za-z]+\s*\d+\s*[/\\-_]?\s*\d+', dir_name):
                    block_folders.append(os.path.join(root, dir_name))

                    # Store the normalized name for later matching
                    normalized = self.normalize_block_name(dir_name)
                    if normalized:
                        if normalized not in self.block_name_variants:
                            self.block_name_variants[normalized] = []
                        self.block_name_variants[normalized].append(dir_name)

        logger.info(f"Found {len(block_folders)} potential block folders with {len(self.block_name_variants)} unique patterns")
        return self.block_name_variants

    def find_detection_files(self, progress_callback=None):
        """Find detection point files for each block.

        Args:
            progress_callback (function): Callback function for progress updates

        Returns:
            dict: Dictionary mapping block names to detection file paths
        """
        logger.info(f"Searching for detection point files in {self.root_dir}")
        self.detection_files = {}
        all_point_files = []

        # First analyze directory structure to identify naming patterns
        self.analyze_directory_structure()

        # Get all sub-division folders except "Combined Results"
        sub_div_folders = [f for f in os.listdir(self.root_dir)
                          if os.path.isdir(os.path.join(self.root_dir, f))
                          and f.lower() != "combined results"]

        total_folders = len(sub_div_folders)
        for i, sub_div in enumerate(sub_div_folders):
            sub_div_path = os.path.join(self.root_dir, sub_div)
            logger.info(f"Scanning sub-division: {sub_div}")

            # Look for "Hasil Deteksi Point" folder (case-insensitive)
            detection_folders = []
            for folder in os.listdir(sub_div_path):
                folder_path = os.path.join(sub_div_path, folder)
                if os.path.isdir(folder_path) and "deteksi point" in folder.lower():
                    detection_folders.append(folder_path)

            if not detection_folders:
                # Look for any folder with "point" in the name as a fallback
                for folder in os.listdir(sub_div_path):
                    folder_path = os.path.join(sub_div_path, folder)
                    if os.path.isdir(folder_path) and "point" in folder.lower():
                        detection_folders.append(folder_path)

            if not detection_folders:
                logger.warning(f"No detection point folder found in {sub_div}")
                continue

            # Process each detection point folder
            for detection_folder in detection_folders:
                logger.info(f"Processing detection folder: {detection_folder}")

                # Get all block folders
                block_folders = [f for f in os.listdir(detection_folder)
                               if os.path.isdir(os.path.join(detection_folder, f))]

                for block_folder in block_folders:
                    block_path = os.path.join(detection_folder, block_folder)

                    # Find point shapefile in block folder
                    point_files = glob.glob(os.path.join(block_path, "*.shp"))
                    # Exclude files with "inclave" in the name
                    point_files = [f for f in point_files if "inclave" not in f.lower()]

                    if point_files:
                        # Use the first non-inclave shapefile found
                        point_file = point_files[0]

                        # Format block name to match boundary shapefile format
                        formatted_block = self.format_block_name_for_matching(block_folder)

                        # Store multiple name variants for this file to improve matching chances
                        variants = [
                            formatted_block,
                            block_folder,
                            self.normalize_block_name(formatted_block)
                        ]

                        # Add the normalized name and its variants
                        normalized = self.normalize_block_name(formatted_block)
                        if normalized in self.block_name_variants:
                            variants.extend(self.block_name_variants[normalized])

                        # Store the file under all variants
                        for variant in set(variants):
                            if variant and variant not in self.detection_files:
                                self.detection_files[variant] = point_file

                        logger.info(f"Found detection file for block {formatted_block}: {point_file}")
                        all_point_files.append(point_file)
                    else:
                        logger.warning(f"No point shapefile found in {block_path}")

            if progress_callback:
                progress_callback((i + 1) / total_folders * 100)

        logger.info(f"Found {len(self.detection_files)} detection file mappings for {len(set(all_point_files))} unique files")
        # Store all point files for spatial processing
        self.all_point_files = all_point_files
        return self.detection_files

    def format_block_name_for_matching(self, block_name):
        """Format block name from folder format to boundary format.

        Args:
            block_name (str): Block name in folder format (e.g., "P_08_01")

        Returns:
            str: Block name in boundary format (e.g., "P 08 / 01")
        """
        # Remove any file extension if present
        block_name = os.path.splitext(block_name)[0]

        # Handle different formats
        if "_" in block_name:
            # Format: P_08_01 -> P 08 / 01
            parts = block_name.split("_")
            if len(parts) >= 3:
                return f"{parts[0]} {parts[1]} / {parts[2]}"

        return block_name

    def format_block_name_for_folder(self, block_name):
        """Format block name from boundary format to folder format.

        Args:
            block_name (str): Block name in boundary format (e.g., "P 08 / 01")

        Returns:
            str: Block name in folder format (e.g., "P_08_01")
        """
        # Handle format: P 08 / 01 -> P_08_01
        if " " in block_name and "/" in block_name:
            block_name = block_name.replace(" ", "_").replace("/", "_").replace("__", "_")

        return block_name

    def find_matching_detection_file(self, block_name):
        """Find matching detection file for a block name using fuzzy matching.

        Args:
            block_name (str): Block name to find matching file for

        Returns:
            str: Path to matching detection file, or None if no match found
        """
        if not block_name:
            return None

        # Try direct lookup first
        if block_name in self.detection_files:
            return self.detection_files[block_name]

        # Try formatted version
        formatted_block = self.format_block_name_for_matching(block_name)
        if formatted_block in self.detection_files:
            return self.detection_files[formatted_block]

        # Try folder version
        folder_block = self.format_block_name_for_folder(block_name)
        if folder_block in self.detection_files:
            return self.detection_files[folder_block]

        # Try normalized version
        normalized = self.normalize_block_name(block_name)
        if normalized in self.detection_files:
            return self.detection_files[normalized]

        # Try to find similar names
        for key in self.detection_files:
            if self.similar_block_name(block_name, key):
                logger.info(f"Found similar block name match: '{block_name}' ~ '{key}'")
                return self.detection_files[key]

        return None

    def load_combined_point_file(self):
        """Load combined detection point shapefile.

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.combined_point_file:
            logger.error("No combined point file specified")
            return False

        try:
            logger.info(f"Loading combined detection point file: {self.combined_point_file}")
            self.combined_points_gdf = gpd.read_file(self.combined_point_file)
            logger.info(f"Loaded {len(self.combined_points_gdf)} detection points")

            # Check if the required attributes exist
            required_attrs = ['SUB_DIVISI', 'BLOK']
            missing_attrs = [attr for attr in required_attrs if attr not in self.combined_points_gdf.columns]

            if missing_attrs:
                logger.warning(f"Combined point file is missing required attributes: {missing_attrs}")
                logger.info(f"Available columns: {list(self.combined_points_gdf.columns)}")
                # Try alternative column names if exact matches not found
                for attr in missing_attrs:
                    for col in self.combined_points_gdf.columns:
                        if attr.lower() in col.lower():
                            logger.info(f"Using '{col}' as substitute for '{attr}'")
                            self.combined_points_gdf[attr] = self.combined_points_gdf[col]
                            missing_attrs.remove(attr)
                            break

            if missing_attrs:
                logger.error(f"Combined point file is missing required attributes: {missing_attrs}")
                return False

            # Set flag to use combined file
            self.use_combined_file = True

            # Ensure CRS matches boundary file if loaded
            if self.boundary_gdf is not None and self.combined_points_gdf.crs != self.boundary_gdf.crs:
                logger.info(f"Reprojecting points from {self.combined_points_gdf.crs} to {self.boundary_gdf.crs}")
                self.combined_points_gdf = self.combined_points_gdf.to_crs(self.boundary_gdf.crs)

            return True
        except Exception as e:
            logger.error(f"Error loading combined point file: {str(e)}")
            self.errors.append(f"Error loading combined point file: {str(e)}")
            return False

    def merge_duplicate_boundaries(self, updated_gdf, duplicate_blocks, combined_points_gdf):
        """Merge duplicate boundaries and calculate tree counts using spatial overlay.

        Args:
            updated_gdf (GeoDataFrame): The boundary GeoDataFrame
            duplicate_blocks (dict): Dictionary of duplicate BLOK and SUB_DIVISI combinations
            combined_points_gdf (GeoDataFrame): Combined detection points GeoDataFrame

        Returns:
            tuple: (merged_results, processed_indices)
                merged_results: Dictionary mapping (BLOK, SUB_DIVISI) to (tree_count, area_ha, sph)
                processed_indices: Set of indices that have been processed
        """
        merged_results = {}
        processed_indices = set()

        # Process each duplicate block combination
        for (block_name, sub_divisi), count in duplicate_blocks.items():
            logger.info(f"Merging duplicate boundaries for {block_name} in {sub_divisi}")

            # Filter to get all polygons with this BLOK and SUB_DIVISI
            block_mask = (updated_gdf['BLOK'] == block_name) & (updated_gdf['SUB_DIVISI'] == sub_divisi)
            block_polygons = updated_gdf[block_mask].copy()

            # Skip if no polygons found (shouldn't happen, but just in case)
            if len(block_polygons) == 0:
                logger.warning(f"No polygons found for {block_name} in {sub_divisi} despite being in duplicates list")
                continue

            # Get indices of these polygons for later reference
            block_indices = block_polygons.index.tolist()
            processed_indices.update(block_indices)

            # Separate non-inclave (HCv=0) and inclave (HCv=1) polygons if HCv column exists
            non_inclave_polygons = block_polygons
            inclave_polygons = None
            total_inclave_area = 0

            if 'HCv' in block_polygons.columns:
                non_inclave_polygons = block_polygons[block_polygons['HCv'] == 0]
                inclave_polygons = block_polygons[block_polygons['HCv'] == 1]

                # Calculate total inclave area if any inclave polygons exist
                if len(inclave_polygons) > 0:
                    # Use appropriate area column
                    area_col = None
                    for col in ['luas_netto', 'LUAS_NETTO', 'Luas_Netto', 'LUAS_AUTO', 'luas_aut_1']:
                        if col in inclave_polygons.columns:
                            area_col = col
                            break

                    if area_col:
                        total_inclave_area = inclave_polygons[area_col].sum()
                    else:
                        # If no area column found, calculate from geometry
                        total_inclave_area = inclave_polygons.geometry.area.sum() / 10000  # Convert to hectares

            # Skip if no non-inclave polygons found
            if len(non_inclave_polygons) == 0:
                logger.warning(f"No non-inclave polygons found for {block_name} in {sub_divisi}")
                continue

            # Dissolve the non-inclave polygons to create a single merged polygon
            try:
                merged_polygon = non_inclave_polygons.dissolve().geometry.iloc[0]

                # Calculate the total area of the merged polygon
                # First try to use the area columns
                total_area = 0
                for col in ['luas_netto', 'LUAS_NETTO', 'Luas_Netto', 'LUAS_AUTO', 'luas_aut_1']:
                    if col in non_inclave_polygons.columns:
                        total_area = non_inclave_polygons[col].sum()
                        break

                # If no area column found or area is zero, calculate from geometry
                if total_area <= 0:
                    total_area = merged_polygon.area / 10000  # Convert to hectares

                # Calculate net area (total area minus inclave area)
                net_area = total_area - total_inclave_area
                if net_area <= 0:
                    logger.warning(f"Net area for {block_name} in {sub_divisi} is zero or negative: {net_area}")
                    net_area = total_area  # Fallback to total area

                # Count points within the merged polygon using spatial overlay
                if combined_points_gdf is not None:
                    points_in_polygon = combined_points_gdf[combined_points_gdf.geometry.within(merged_polygon)]
                    tree_count = len(points_in_polygon)

                    # Calculate SPH (stems per hectare)
                    sph = 0
                    if net_area > 0 and tree_count > 0:
                        sph = round(tree_count / net_area, 2)

                    # Check if SPH is above 150, if so divide tree count and SPH by 2
                    # This corrects for potential double counting of trees
                    if sph > 150:
                        tree_count = round(tree_count / 2)
                        sph = round(sph / 2, 2)
                        logger.info(f"SPH was above 150, correcting for double counting: {block_name}")

                    # Store the results for later updating
                    merged_results[(block_name, sub_divisi)] = (tree_count, net_area, sph)

                    logger.info(f"Merged {count} polygons for {block_name} in {sub_divisi}: "
                               f"{tree_count} trees, {net_area:.2f} ha, SPH: {sph}")
                else:
                    logger.warning(f"No combined points GeoDataFrame available for spatial overlay")
            except Exception as e:
                logger.error(f"Error merging polygons for {block_name} in {sub_divisi}: {str(e)}")

        return merged_results, processed_indices

    def update_tree_counts(self, progress_callback=None):
        """Update tree counts and SPH values in boundary shapefile using spatial join or name matching.

        Args:
            progress_callback (function): Callback function for progress updates

        Returns:
            bool: True if successful, False otherwise
        """
        if self.boundary_gdf is None:
            logger.error("Boundary shapefile not loaded")
            return False

        # Check for combined file first
        if self.use_combined_file:
            if self.combined_points_gdf is None:
                logger.error("Combined point file not loaded")
                return False
        elif self.update_method in ["spatial", "hybrid"]:
            if not hasattr(self, 'all_point_files') or not self.all_point_files:
                logger.error("No detection files found")
                return False
        elif self.update_method == "name":
            if not self.detection_files:
                logger.error("No detection files found")
                return False

        logger.info(f"Updating tree counts in boundary shapefile using {self.update_method} method")

        # Create a copy of the boundary GeoDataFrame to avoid modifying the original
        updated_gdf = self.boundary_gdf.copy()

        # Filter out inclave polygons if not updating them
        if not self.update_inclave:
            # Check if HCv column exists
            if 'HCv' in updated_gdf.columns:
                original_count = len(updated_gdf)
                updated_gdf = updated_gdf[updated_gdf['HCv'] == 0]
                logger.info(f"Filtered out inclave polygons: {original_count - len(updated_gdf)} polygons removed")

        # Identify duplicate BLOK and SUB_DIVISI combinations
        # This will help us detect cases where we need to use spatial method
        duplicate_blocks = {}
        if 'BLOK' in updated_gdf.columns and 'SUB_DIVISI' in updated_gdf.columns:
            # Count occurrences of each BLOK and SUB_DIVISI combination
            block_counts = updated_gdf.groupby(['BLOK', 'SUB_DIVISI']).size().reset_index(name='count')
            # Filter to only those with more than one occurrence
            duplicates = block_counts[block_counts['count'] > 1]

            # Create a dictionary of duplicate blocks for quick lookup
            for _, row in duplicates.iterrows():
                key = (row['BLOK'], row['SUB_DIVISI'])
                duplicate_blocks[key] = row['count']

            if duplicate_blocks:
                logger.info(f"Found {len(duplicate_blocks)} BLOK and SUB_DIVISI combinations with duplicates")
                for (blok, sub_div), count in duplicate_blocks.items():
                    logger.info(f"  - {blok} in {sub_div}: {count} occurrences")

        # Also identify duplicate boundary-x features
        boundary_features = {}
        if 'ID_Feature' in updated_gdf.columns:
            # Get all ID_Feature values that start with 'boundary-'
            boundary_mask = updated_gdf['ID_Feature'].astype(str).str.startswith('boundary-')
            if boundary_mask.any():
                # Count occurrences of each boundary-x feature
                boundary_counts = updated_gdf[boundary_mask]['ID_Feature'].value_counts()
                # Filter to only those with more than one occurrence
                duplicate_boundaries = boundary_counts[boundary_counts > 1]

                # Create a dictionary of duplicate boundary features for quick lookup
                for feature, count in duplicate_boundaries.items():
                    boundary_features[feature] = count

                if boundary_features:
                    logger.info(f"Found {len(boundary_features)} boundary features with duplicates")
                    for feature, count in boundary_features.items():
                        logger.info(f"  - {feature}: {count} occurrences")

        # Load and prepare combined points if needed
        combined_points_gdf = None

        # If using combined file, use it directly
        if self.use_combined_file:
            combined_points_gdf = self.combined_points_gdf
            # Ensure CRS matches
            if combined_points_gdf.crs != updated_gdf.crs:
                logger.info(f"Reprojecting points from {combined_points_gdf.crs} to {updated_gdf.crs}")
                combined_points_gdf = combined_points_gdf.to_crs(updated_gdf.crs)
        # Otherwise load individual files if needed for spatial method
        elif self.update_method in ["spatial", "hybrid"]:
            # Combine all detection point files into one GeoDataFrame
            logger.info("Loading and combining all detection point files")
            combined_points_gdf = None

            total_files = len(self.all_point_files)
            for i, point_file in enumerate(self.all_point_files):
                try:
                    point_gdf = gpd.read_file(point_file)

                    if combined_points_gdf is None:
                        combined_points_gdf = point_gdf
                    else:
                        combined_points_gdf = pd.concat([combined_points_gdf, point_gdf])

                    logger.info(f"Loaded {len(point_gdf)} points from {point_file}")

                    if progress_callback:
                        progress_callback((i + 1) / (total_files * 2) * 100)  # First half of progress
                except Exception as e:
                    logger.error(f"Error loading point file {point_file}: {str(e)}")
                    self.errors.append(f"Error loading point file {point_file}: {str(e)}")

            if combined_points_gdf is None or len(combined_points_gdf) == 0:
                logger.error("No valid detection points found")
                return False

            logger.info(f"Combined {len(combined_points_gdf)} detection points")

            # Ensure both GeoDataFrames have the same CRS
            if combined_points_gdf.crs != updated_gdf.crs:
                logger.info(f"Reprojecting points from {combined_points_gdf.crs} to {updated_gdf.crs}")
                combined_points_gdf = combined_points_gdf.to_crs(updated_gdf.crs)

        # Process duplicate blocks first if spatial method is enabled
        merged_results = {}
        processed_indices = set()

        if self.force_spatial_for_duplicates and combined_points_gdf is not None:
            if duplicate_blocks:
                logger.info("Processing duplicate blocks using spatial overlay method")
                merged_results, processed_indices = self.merge_duplicate_boundaries(
                    updated_gdf, duplicate_blocks, combined_points_gdf
                )
                logger.info(f"Processed {len(processed_indices)} polygons in {len(merged_results)} duplicate block groups")

        # Process each row in the boundary shapefile
        logger.info("Processing polygons")
        total_rows = len(updated_gdf)
        for i, (idx, row) in enumerate(updated_gdf.iterrows()):
            # Skip if this index was already processed in the duplicate blocks step
            if idx in processed_indices:
                # Get the block name and sub_divisi for this row
                block_name = row.get('BLOK', '')
                sub_divisi = row.get('SUB_DIVISI', '')

                # If we have results for this block, apply them
                if (block_name, sub_divisi) in merged_results:
                    tree_count, area_ha, sph = merged_results[(block_name, sub_divisi)]

                    # Update the row with the merged results
                    updated_gdf.at[idx, 'Jumlah_Poh'] = tree_count
                    updated_gdf.at[idx, 'SPH'] = sph

                    # Record the update
                    self.updates.append({
                        'ID_Feature': row.get('ID_Feature', ''),
                        'BLOK': block_name,
                        'SUB_DIVISI': sub_divisi,
                        'Old_Count': row.get('Jumlah_Poh', 0),
                        'New_Count': tree_count,
                        'Area_Ha': area_ha,
                        'SPH': sph,
                        'Method': 'Spatial (merged boundaries)'
                    })
                continue

            try:
                id_feature = row.get('ID_Feature', '')
                block_name = row.get('BLOK', '')
                hcv_category = row.get('HCV_Catego', 0)
                sub_divisi = row.get('SUB_DIVISI', '')

                # Check if this is an inclave (by ID_Feature or HCV_Category)
                is_inclave = False
                if isinstance(id_feature, str) and id_feature.startswith('Inclave-'):
                    is_inclave = True
                    logger.info(f"Feature {id_feature} is an inclave (by ID_Feature)")
                elif hcv_category == 1:
                    is_inclave = True
                    logger.info(f"Feature {id_feature} is an inclave (by HCV_Catego=1)")

                # For inclave features, set tree count and SPH to 0
                if is_inclave:
                    updated_gdf.at[idx, 'Jumlah_Poh'] = 0
                    updated_gdf.at[idx, 'SPH'] = 0

                    self.updates.append({
                        'ID_Feature': id_feature,
                        'BLOK': block_name,
                        'SUB_DIVISI': sub_divisi,
                        'Old_Count': row.get('Jumlah_Poh', 0),
                        'New_Count': 0,
                        'Area_Ha': 0,
                        'SPH': 0,
                        'Method': 'Inclave (skipped)'
                    })
                    continue

                if not block_name:
                    logger.warning(f"Row {idx} (ID_Feature: {id_feature}) has no BLOK value, skipping")
                    continue

                # Get area in hectares - use luas_netto instead of luas_auto
                area_ha = row.get('luas_netto', 0)
                if area_ha <= 0:
                    area_ha = row.get('LUAS_NETTO', 0)
                if area_ha <= 0:
                    area_ha = row.get('Luas_Netto', 0)

                # Fallback to other area fields if luas_netto not found or is zero
                if area_ha <= 0:
                    area_ha = row.get('LUAS_AUTO', 0)
                if area_ha <= 0:
                    area_ha = row.get('luas_aut_1', 0)

                tree_count = 0
                method_used = "None"

                # Check if this is a special case that requires spatial method:
                # 1. HCV = 0 (non-inclave) AND
                # 2. Either:
                #    a. ID_Feature starts with "boundary-" and has duplicates, or
                #    b. BLOK and SUB_DIVISI combination has duplicates
                force_spatial = False

                # Only check for special cases if the option is enabled
                if self.force_spatial_for_duplicates:
                    # Check for HCV = 0
                    hcv_value = row.get('HCv', None)
                    if hcv_value == 0:
                        # Check for duplicate boundary feature
                        if isinstance(id_feature, str) and id_feature.startswith('boundary-') and id_feature in boundary_features:
                            force_spatial = True
                            logger.info(f"Forcing spatial method for {id_feature} (duplicate boundary feature)")
                        # Check for duplicate BLOK and SUB_DIVISI combination
                        elif block_name and sub_divisi and (block_name, sub_divisi) in duplicate_blocks:
                            force_spatial = True
                            logger.info(f"Forcing spatial method for {block_name} in {sub_divisi} (duplicate block)")

                # If using combined file with attribute matching
                if self.use_combined_file:
                    try:
                        # If this is a special case, use spatial method directly
                        if force_spatial:
                            polygon = row.geometry
                            points_in_polygon = combined_points_gdf[combined_points_gdf.geometry.within(polygon)]
                            spatial_tree_count = len(points_in_polygon)

                            tree_count = spatial_tree_count
                            method_used = "Spatial (forced)"
                            logger.info(f"Forced spatial method for {id_feature} ({sub_divisi} {block_name}): found {spatial_tree_count} points")
                        else:
                            # Regular attribute matching
                            matching_points = combined_points_gdf

                            # Filter by SUB_DIVISI if available
                            if sub_divisi and 'SUB_DIVISI' in combined_points_gdf.columns:
                                matching_points = matching_points[matching_points['SUB_DIVISI'] == sub_divisi]

                            # Filter by BLOK
                            if 'BLOK' in combined_points_gdf.columns:
                                matching_points = matching_points[matching_points['BLOK'] == block_name]

                            # If we found matching points by attributes
                            if not matching_points.empty:
                                attribute_tree_count = len(matching_points)
                                tree_count = attribute_tree_count
                                method_used = "Attribute matching"
                                logger.info(f"Found {attribute_tree_count} points for {sub_divisi} {block_name} using attribute matching")
                            # If no attribute matches, try spatial match as fallback
                            else:
                                polygon = row.geometry
                                points_in_polygon = combined_points_gdf[combined_points_gdf.geometry.within(polygon)]
                                spatial_tree_count = len(points_in_polygon)

                                if spatial_tree_count > 0:
                                    tree_count = spatial_tree_count
                                    method_used = "Spatial (fallback)"
                                    logger.info(f"Used spatial fallback for {sub_divisi} {block_name}: found {spatial_tree_count} points")
                    except Exception as e:
                        logger.error(f"Error in attribute matching for row {idx}: {str(e)}")
                        continue
                # Use existing methods if not using combined file
                else:
                    # If this is a special case or spatial method is selected, use spatial method
                    if force_spatial or self.update_method in ["spatial", "hybrid"]:
                        try:
                            polygon = row.geometry
                            # Filter points within this polygon
                            points_in_polygon = combined_points_gdf[combined_points_gdf.geometry.within(polygon)]
                            spatial_tree_count = len(points_in_polygon)

                            if spatial_tree_count > 0 or self.update_method == "spatial" or force_spatial:
                                tree_count = spatial_tree_count
                                method_used = "Spatial" if not force_spatial else "Spatial (forced)"
                                if force_spatial:
                                    logger.info(f"Forced spatial method for {id_feature} ({sub_divisi} {block_name}): found {spatial_tree_count} points")
                        except Exception as e:
                            logger.error(f"Error in spatial method for row {idx}: {str(e)}")
                            if self.update_method == "spatial" or force_spatial:
                                continue

                    # Try name matching if spatial didn't work or hybrid/name is selected (and not forced spatial)
                    if not force_spatial and ((tree_count == 0 and self.update_method == "hybrid") or self.update_method == "name"):
                        try:
                            # Use enhanced name matching
                            detection_file = self.find_matching_detection_file(block_name)

                            if detection_file:
                                # Load detection point shapefile
                                point_gdf = gpd.read_file(detection_file)
                                name_tree_count = len(point_gdf)

                                tree_count = name_tree_count
                                method_used = "Name matching"
                                logger.info(f"Found matching detection file for {block_name} using fuzzy matching")
                        except Exception as e:
                            logger.error(f"Error in name matching for row {idx}: {str(e)}")
                            continue

                # Calculate SPH (stems per hectare)
                sph = 0
                if area_ha > 0 and tree_count > 0:
                    sph = round(tree_count / area_ha, 2)

                # Check if SPH is above 150, if so divide tree count and SPH by 2
                # This corrects for potential double counting of trees
                if sph > 150:
                    tree_count = round(tree_count / 2)
                    sph = round(sph / 2, 2)
                    logger.info(f"SPH was above 150, correcting for double counting: {block_name}")

                # Update the row
                updated_gdf.at[idx, 'Jumlah_Poh'] = tree_count
                updated_gdf.at[idx, 'SPH'] = sph

                logger.info(f"Updated {id_feature} ({sub_divisi} {block_name}): {tree_count} trees, SPH: {sph} using {method_used}")
                self.updates.append({
                    'ID_Feature': id_feature,
                    'BLOK': block_name,
                    'SUB_DIVISI': sub_divisi,
                    'Old_Count': row.get('Jumlah_Poh', 0),
                    'New_Count': tree_count,
                    'Area_Ha': area_ha,
                    'SPH': sph,
                    'Method': method_used
                })
            except Exception as e:
                logger.error(f"Error processing row {idx}: {str(e)}")
                self.errors.append(f"Error processing row {idx}: {str(e)}")

            if progress_callback:
                progress_callback(50 + (i + 1) / total_rows * 50)  # Second half of progress

        # Update the boundary GeoDataFrame
        self.boundary_gdf = updated_gdf

        logger.info(f"Updated {len(self.updates)} blocks with tree counts")
        return True

    def save_updated_shapefile(self, output_file):
        """Save updated boundary shapefile.

        Args:
            output_file (str): Path to output shapefile

        Returns:
            bool: True if successful, False otherwise
        """
        if self.boundary_gdf is None:
            logger.error("No boundary data to save")
            return False

        try:
            logger.info(f"Saving updated shapefile to {output_file}")

            # Create output directory if it doesn't exist
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Save the updated GeoDataFrame
            self.boundary_gdf.to_file(output_file)

            logger.info(f"Saved updated shapefile with {len(self.updates)} updated blocks")
            return True
        except Exception as e:
            logger.error(f"Error saving updated shapefile: {str(e)}")
            self.errors.append(f"Error saving updated shapefile: {str(e)}")
            return False

    def generate_summary(self):
        """Generate summary of updates.

        Returns:
            str: Summary text
        """
        summary = f"Tree Count Update Summary\n"
        summary += f"========================\n\n"

        summary += f"Boundary File: {self.boundary_file}\n"
        summary += f"Root Directory: {self.root_dir}\n"
        summary += f"Update Method: {self.update_method}\n"
        summary += f"Update Inclave Polygons: {'Yes' if self.update_inclave else 'No'}\n"
        summary += f"Merge Duplicate Boundaries: {'Yes' if self.force_spatial_for_duplicates else 'No'}\n\n"

        summary += f"Total Blocks in Boundary File: {len(self.boundary_gdf) if self.boundary_gdf is not None else 0}\n"
        summary += f"Total Detection Files Found: {len(self.detection_files)}\n"
        summary += f"Total Blocks Updated: {len(self.updates)}\n"
        summary += f"Total Errors: {len(self.errors)}\n\n"

        # Count merged boundaries
        merged_count = sum(1 for update in self.updates if update.get('Method', '') == 'Spatial (merged boundaries)')
        if merged_count > 0:
            summary += f"Merged Boundaries: {merged_count} polygons were updated using the merged boundaries approach\n\n"

        # Group updates by method for better analysis
        method_counts = {}
        for update in self.updates:
            method = update.get('Method', 'Unknown')
            if method not in method_counts:
                method_counts[method] = 0
            method_counts[method] += 1

        summary += f"Update Methods Used:\n"
        for method, count in method_counts.items():
            summary += f"- {method}: {count} polygons\n"
        summary += "\n"

        if self.updates:
            summary += f"Updated Blocks:\n"
            summary += f"ID_Feature\tBLOK\tSUB_DIVISI\tOld Count\tNew Count\tArea (ha)\tSPH\tMethod\n"
            for update in self.updates:
                summary += f"{update['ID_Feature']}\t{update['BLOK']}\t{update['SUB_DIVISI']}\t{update['Old_Count']}\t{update['New_Count']}\t{update['Area_Ha']}\t{update['SPH']}\t{update.get('Method', 'Unknown')}\n"

        if self.errors:
            summary += f"\nErrors:\n"
            for error in self.errors:
                summary += f"- {error}\n"

        return summary


class TreeCountUpdaterGUI:
    """GUI for TreeCountUpdater."""

    def __init__(self, root):
        """Initialize the GUI.

        Args:
            root (tk.Tk): Root Tkinter window
        """
        self.root = root
        self.root.title("Tree Count Updater")
        self.root.geometry("1000x700")

        self.updater = TreeCountUpdater()

        self.setup_gui()

    def setup_gui(self):
        """Set up the GUI interface."""
        # Create main frame with two columns
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Left frame for inputs
        left_frame = ttk.Frame(main_frame, width=400)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Right frame for results
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Input frame
        input_frame = ttk.LabelFrame(left_frame, text="Input Configuration", padding="10")
        input_frame.pack(fill=tk.X, pady=5)

        # Boundary file selection
        boundary_frame = ttk.Frame(input_frame)
        boundary_frame.pack(fill=tk.X, pady=5)

        ttk.Label(boundary_frame, text="Boundary File:", width=15).pack(side=tk.LEFT, padx=5)
        self.boundary_var = tk.StringVar()
        ttk.Entry(boundary_frame, textvariable=self.boundary_var, width=40).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(boundary_frame, text="Browse...", command=self.browse_boundary_file).pack(side=tk.RIGHT)

        # Input selection frame (directory or combined file)
        input_type_frame = ttk.LabelFrame(left_frame, text="Detection Points Source", padding="10")
        input_type_frame.pack(fill=tk.X, pady=5)

        # Mode selection radiobuttons
        self.input_mode_var = tk.StringVar(value="directory")

        mode_frame = ttk.Frame(input_type_frame)
        mode_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(mode_frame, text="Use individual files in directory",
                       variable=self.input_mode_var, value="directory",
                       command=self.toggle_input_mode).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_frame, text="Use combined detection point file",
                       variable=self.input_mode_var, value="combined",
                       command=self.toggle_input_mode).pack(side=tk.LEFT, padx=5)

        # Root directory selection
        self.dir_frame = ttk.Frame(input_type_frame)
        self.dir_frame.pack(fill=tk.X, pady=5)

        ttk.Label(self.dir_frame, text="Root Directory:", width=15).pack(side=tk.LEFT, padx=5)
        self.root_dir_var = tk.StringVar()
        ttk.Entry(self.dir_frame, textvariable=self.root_dir_var, width=40).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(self.dir_frame, text="Browse...", command=self.browse_root_dir).pack(side=tk.RIGHT)

        # Combined file selection
        self.combined_frame = ttk.Frame(input_type_frame)

        ttk.Label(self.combined_frame, text="Combined File:", width=15).pack(side=tk.LEFT, padx=5)
        self.combined_var = tk.StringVar()
        ttk.Entry(self.combined_frame, textvariable=self.combined_var, width=40).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(self.combined_frame, text="Browse...", command=self.browse_combined_file).pack(side=tk.RIGHT)

        # Output file selection
        output_frame = ttk.LabelFrame(left_frame, text="Output", padding="10")
        output_frame.pack(fill=tk.X, pady=5)

        output_file_frame = ttk.Frame(output_frame)
        output_file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_file_frame, text="Output File:", width=15).pack(side=tk.LEFT, padx=5)
        self.output_var = tk.StringVar()
        ttk.Entry(output_file_frame, textvariable=self.output_var, width=40).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(output_file_frame, text="Browse...", command=self.browse_output_file).pack(side=tk.RIGHT)

        # Options frame
        options_frame = ttk.LabelFrame(left_frame, text="Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)

        # Update method selection
        method_frame = ttk.Frame(options_frame)
        method_frame.pack(fill=tk.X, pady=5)

        ttk.Label(method_frame, text="Update Method:", width=15).pack(side=tk.LEFT, padx=5)
        self.method_var = tk.StringVar(value="hybrid")
        method_combo = ttk.Combobox(method_frame, textvariable=self.method_var, width=40)
        method_combo['values'] = ("spatial", "name", "hybrid", "attribute")
        method_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Inclave update checkbox
        inclave_frame = ttk.Frame(options_frame)
        inclave_frame.pack(fill=tk.X, pady=5)

        self.inclave_var = tk.BooleanVar(value=False)
        inclave_check = ttk.Checkbutton(inclave_frame, text="Update Inclave Polygons", variable=self.inclave_var)
        inclave_check.pack(side=tk.LEFT, padx=5)

        # Force spatial method for duplicates checkbox
        spatial_frame = ttk.Frame(options_frame)
        spatial_frame.pack(fill=tk.X, pady=5)

        self.force_spatial_var = tk.BooleanVar(value=True)
        spatial_check = ttk.Checkbutton(
            spatial_frame,
            text="Merge Duplicate Blocks/Boundaries Using Spatial Method",
            variable=self.force_spatial_var
        )
        spatial_check.pack(side=tk.LEFT, padx=5)

        # Add tooltip/help text
        ttk.Label(
            spatial_frame,
            text="(Recommended for blocks with same name but different areas)",
            foreground="gray"
        ).pack(side=tk.LEFT, padx=5)

        # Add more detailed explanation
        explanation_frame = ttk.Frame(options_frame)
        explanation_frame.pack(fill=tk.X, pady=5)

        explanation_text = (
            "When enabled, blocks with the same BLOK and SUB_DIVISI names will be merged. "
            "Their boundaries will be combined, and tree counts will be calculated using spatial overlay. "
            "This is useful when a block is divided into multiple polygons."
        )

        explanation_label = ttk.Label(
            explanation_frame,
            text=explanation_text,
            foreground="gray",
            wraplength=380,
            justify="left"
        )
        explanation_label.pack(side=tk.LEFT, padx=5, fill=tk.X)

        # Process buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.find_files_button = tk.Button(
            button_frame,
            text="Find/Load Detection Files",
            command=self.find_detection_files,
            bg="#2196F3",
            fg="white",
            height=2,
            width=20
        )
        self.find_files_button.pack(side=tk.LEFT, padx=5)

        self.update_button = tk.Button(
            button_frame,
            text="Update Tree Counts",
            command=self.update_tree_counts,
            bg="#4CAF50",
            fg="white",
            height=2,
            width=20,
            state=tk.DISABLED
        )
        self.update_button.pack(side=tk.LEFT, padx=5)

        self.save_button = tk.Button(
            button_frame,
            text="Save Updated Shapefile",
            command=self.save_updated_shapefile,
            bg="#FF9800",
            fg="white",
            height=2,
            width=20,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5)

        # Progress bar
        progress_frame = ttk.Frame(left_frame)
        progress_frame.pack(fill=tk.X, pady=5)

        ttk.Label(progress_frame, text="Progress:").pack(side=tk.LEFT, padx=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Results frame
        self.results_frame = ttk.LabelFrame(right_frame, text="Results", padding="10")
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Create a text widget to display results
        self.results_text = tk.Text(self.results_frame, height=10, width=50)
        self.results_text.pack(fill=tk.BOTH, expand=True)

        # Add a scrollbar
        scrollbar = ttk.Scrollbar(self.results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Initialize UI state
        self.toggle_input_mode()

    def toggle_input_mode(self):
        """Toggle between directory and combined file input modes."""
        mode = self.input_mode_var.get()

        if mode == "directory":
            self.dir_frame.pack(fill=tk.X, pady=5)
            self.combined_frame.pack_forget()
        else:  # combined
            self.dir_frame.pack_forget()
            self.combined_frame.pack(fill=tk.X, pady=5)

    def browse_boundary_file(self):
        """Browse for boundary polygon shapefile."""
        file_path = filedialog.askopenfilename(
            title="Select Boundary Polygon File",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.boundary_var.set(file_path)
            self.updater.boundary_file = file_path

            # Suggest output filename based on input
            if not self.output_var.get():
                input_name = os.path.splitext(os.path.basename(file_path))[0]
                output_dir = os.path.dirname(file_path)
                output_path = os.path.join(output_dir, f"{input_name}_updated.shp")
                self.output_var.set(output_path)

    def browse_root_dir(self):
        """Browse for root directory containing sub-division folders."""
        dir_path = filedialog.askdirectory(
            title="Select Root Directory"
        )
        if dir_path:
            self.root_dir_var.set(dir_path)
            self.updater.root_dir = dir_path

    def browse_combined_file(self):
        """Browse for combined detection point shapefile."""
        file_path = filedialog.askopenfilename(
            title="Select Combined Detection Point File",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.combined_var.set(file_path)
            self.updater.combined_point_file = file_path

    def browse_output_file(self):
        """Browse for output shapefile."""
        file_path = filedialog.asksaveasfilename(
            title="Save Output Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
            defaultextension=".shp"
        )
        if file_path:
            self.output_var.set(file_path)

    def update_progress(self, value):
        """Update progress bar.

        Args:
            value (float): Progress value (0-100)
        """
        self.progress_var.set(value)
        self.root.update_idletasks()

    def find_detection_files(self):
        """Find detection point files."""
        # Check if boundary file is selected
        if not self.boundary_var.get():
            messagebox.showerror("Error", "Please select boundary file first")
            return

        # Check input mode
        mode = self.input_mode_var.get()

        if mode == "directory" and not self.root_dir_var.get():
            messagebox.showerror("Error", "Please select root directory first")
            return

        if mode == "combined" and not self.combined_var.get():
            messagebox.showerror("Error", "Please select combined detection point file first")
            return

        # Disable the button to prevent multiple clicks
        self.find_files_button.config(state=tk.DISABLED)
        self.status_var.set("Loading detection files...")
        self.root.update()

        try:
            # Update updater with selected files
            self.updater.boundary_file = self.boundary_var.get()

            if mode == "directory":
                self.updater.root_dir = self.root_dir_var.get()
                self.updater.combined_point_file = None
                self.updater.use_combined_file = False
            else:  # combined
                self.updater.combined_point_file = self.combined_var.get()
                self.updater.root_dir = None

            # Load boundary file
            if not self.updater.load_boundary_file():
                messagebox.showerror("Error", "Failed to load boundary file")
                return

            # Load files based on mode
            if mode == "directory":
                # Find detection files in directory
                detection_files = self.updater.find_detection_files(self.update_progress)

                # Display results
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, f"Found {len(detection_files)} detection files:\n\n")

                for block, file_path in detection_files.items():
                    self.results_text.insert(tk.END, f"{block}: {file_path}\n")

                self.status_var.set(f"Found {len(detection_files)} detection files")
            else:  # combined
                # Load combined point file
                if not self.updater.load_combined_point_file():
                    messagebox.showerror("Error", "Failed to load combined detection point file")
                    return

                # Display results
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, f"Loaded combined detection point file:\n\n")
                self.results_text.insert(tk.END, f"File: {self.updater.combined_point_file}\n")
                self.results_text.insert(tk.END, f"Points: {len(self.updater.combined_points_gdf)}\n\n")

                # Show column information
                self.results_text.insert(tk.END, "Columns:\n")
                for col in self.updater.combined_points_gdf.columns:
                    self.results_text.insert(tk.END, f"- {col}\n")

                self.status_var.set(f"Loaded combined detection point file with {len(self.updater.combined_points_gdf)} points")

            # Enable update button
            self.update_button.config(state=tk.NORMAL)

        except Exception as e:
            messagebox.showerror("Error", f"Error finding detection files: {str(e)}")
            self.status_var.set("Error finding detection files")
        finally:
            # Re-enable the button
            self.find_files_button.config(state=tk.NORMAL)

    def update_tree_counts(self):
        """Update tree counts in boundary shapefile."""
        # Disable the button to prevent multiple clicks
        self.update_button.config(state=tk.DISABLED)
        self.status_var.set("Updating tree counts...")
        self.root.update()

        try:
            # Set update method and options
            self.updater.update_method = self.method_var.get()
            self.updater.update_inclave = self.inclave_var.get()
            self.updater.force_spatial_for_duplicates = self.force_spatial_var.get()

            # Update tree counts
            if self.updater.update_tree_counts(self.update_progress):
                # Display results
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, self.updater.generate_summary())

                # Enable save button
                self.save_button.config(state=tk.NORMAL)

                self.status_var.set(f"Updated {len(self.updater.updates)} blocks with tree counts")
            else:
                messagebox.showerror("Error", "Failed to update tree counts")
                self.status_var.set("Failed to update tree counts")
        except Exception as e:
            messagebox.showerror("Error", f"Error updating tree counts: {str(e)}")
            self.status_var.set("Error updating tree counts")
        finally:
            # Re-enable the button
            self.update_button.config(state=tk.NORMAL)

    def save_updated_shapefile(self):
        """Save updated boundary shapefile."""
        # Check if output file is selected
        if not self.output_var.get():
            messagebox.showerror("Error", "Please select output file first")
            return

        # Disable the button to prevent multiple clicks
        self.save_button.config(state=tk.DISABLED)
        self.status_var.set("Saving updated shapefile...")
        self.root.update()

        try:
            # Save updated shapefile
            if self.updater.save_updated_shapefile(self.output_var.get()):
                messagebox.showinfo("Success", f"Updated shapefile saved to {self.output_var.get()}")
                self.status_var.set("Updated shapefile saved successfully")

                # Open output directory
                os.startfile(os.path.dirname(self.output_var.get()))
            else:
                messagebox.showerror("Error", "Failed to save updated shapefile")
                self.status_var.set("Failed to save updated shapefile")
        except Exception as e:
            messagebox.showerror("Error", f"Error saving updated shapefile: {str(e)}")
            self.status_var.set("Error saving updated shapefile")
        finally:
            # Re-enable the button
            self.save_button.config(state=tk.NORMAL)


def main():
    """Main function."""
    root = tk.Tk()
    # Create the GUI application (variable used implicitly by mainloop)
    TreeCountUpdaterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
