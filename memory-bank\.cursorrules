# Cursor Project Intelligence

## Project Structure
- The project consists of three separate tools in their own directories
- Each tool follows a similar pattern with a main Python script and supporting files
- GIS data files (.shp, .dbf, .shx, etc.) should be considered as sets and not altered individually

## Code Patterns
- GUI applications use Tkinter with a consistent structure:
  - Main application class that initializes the UI
  - Event handlers for UI interactions
  - Processing functions separated from UI code where possible
  - Status updates to inform users of progress
- GIS operations follow standard GeoPandas patterns:
  - Load shapefiles with gpd.read_file()
  - Filter with standard DataFrame operations
  - Export with .to_file() method

## Naming Conventions
- Class names: CamelCase (e.g., MapGeoReferencerApp, ColorExtractorApp)
- Function names: snake_case (e.g., load_shapefile, export_to_shapefile)
- Variables: snake_case (e.g., known_coord, boundary_rows)
- Constants: UPPER_SNAKE_CASE (e.g., REPORTLAB_AVAILABLE)

## File Handling
- Always use Path from pathlib for path operations where possible
- Check for file existence before attempting to read
- Provide feedback when files are successfully loaded or saved
- Handle exceptions during file operations and display user-friendly messages

## UI Guidelines
- Use LabelFrame to group related controls
- Provide status updates in a status bar at the bottom
- Offer file browsers for input/output paths
- Show previews of processing results when possible
- Use scrollable canvases for large content

## Data Processing
- HCV_Catego = 0 typically represents boundary areas
- HCV_Catego = 1 typically represents inclave areas (contained within boundaries)
- Division hierarchy: DIVISI > SUB_DIVISI > BLOK
- Area calculations are typically in hectares (ha)

## Error Handling
- Wrap file operations in try/except blocks
- Display specific error messages for common issues
- Validate user inputs before processing
- Check for required data fields before analysis

## Language and Localization
- UI text uses a mix of English and Indonesian
- File paths need to handle spaces and non-ASCII characters
- Messages to users should be clear and informative 