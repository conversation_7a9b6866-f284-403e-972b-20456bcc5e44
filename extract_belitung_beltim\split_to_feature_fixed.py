import geopandas as gpd
import os

print("=== Pemrosesan Area Tanam Belitung dan Belitung Timur (DIPERBAIKI) ===")

# Path file
area_tanam_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/Area_Tanam_Outer_Layer_backup.shp"
batas_desa_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/batas_desa_belitung/batas_desa_belitung.shp"

# Periksa keberadaan file
if not os.path.exists(area_tanam_path):
    print(f"ERROR: File area tanam tidak ditemukan: {area_tanam_path}")
    exit(1)
    
if not os.path.exists(batas_desa_path):
    print(f"ERROR: File batas desa tidak ditemukan: {batas_desa_path}")
    exit(1)

print("✓ File input ditemukan")

# Muat data
print("\n1. Memuat data shapefile...")
area_tanam = gpd.read_file(area_tanam_path)
batas_desa = gpd.read_file(batas_desa_path)

print(f"   - Area tanam: {len(area_tanam)} fitur")
print(f"   - Batas desa: {len(batas_desa)} fitur")

# Periksa dan perbaiki CRS
print("\n2. Memeriksa dan memperbaiki sistem koordinat...")
print(f"   - Area tanam CRS: {area_tanam.crs}")
print(f"   - Batas desa CRS: {batas_desa.crs}")

# Untuk data Indonesia, biasanya menggunakan WGS84 (EPSG:4326) atau UTM Zone 48S (EPSG:32748)
# Karena Belitung berada di sekitar koordinat geografis, kita akan menggunakan WGS84

if area_tanam.crs is None:
    print("   ⚠️  Area tanam tidak memiliki CRS, mengatur ke WGS84...")
    area_tanam = area_tanam.set_crs('EPSG:4326')
    print("   ✓ Area tanam CRS diatur ke WGS84")

if batas_desa.crs is None:
    print("   ⚠️  Batas desa tidak memiliki CRS, mengatur ke WGS84...")
    batas_desa = batas_desa.set_crs('EPSG:4326')  
    print("   ✓ Batas desa CRS diatur ke WGS84")

# Samakan CRS jika berbeda
if area_tanam.crs != batas_desa.crs:
    print("   ⚠️  CRS berbeda, menyamakan ke WGS84...")
    area_tanam = area_tanam.to_crs('EPSG:4326')
    batas_desa = batas_desa.to_crs('EPSG:4326')
    print("   ✓ CRS sudah disamakan")

print(f"   - Final CRS Area tanam: {area_tanam.crs}")
print(f"   - Final CRS Batas desa: {batas_desa.crs}")

# Periksa bounds untuk memastikan data berada di lokasi yang benar
print("\n3. Memeriksa lokasi data...")
at_bounds = area_tanam.total_bounds
bd_bounds = batas_desa.total_bounds

print(f"   Area tanam bounds:")
print(f"     - Longitude: {at_bounds[0]:.6f} to {at_bounds[2]:.6f}")
print(f"     - Latitude: {at_bounds[1]:.6f} to {at_bounds[3]:.6f}")

print(f"   Batas desa bounds:")
print(f"     - Longitude: {bd_bounds[0]:.6f} to {bd_bounds[2]:.6f}")
print(f"     - Latitude: {bd_bounds[1]:.6f} to {bd_bounds[3]:.6f}")

# Periksa apakah koordinat masuk akal untuk wilayah Belitung (sekitar -2° sampai -3° lintang, 107° sampai 108° bujur)
if bd_bounds[0] < 50 or bd_bounds[2] > 150 or bd_bounds[1] < -10 or bd_bounds[3] > 10:
    print("   ⚠️  Koordinat mungkin tidak sesuai untuk wilayah Indonesia")
    print("   💡 Jika data menggunakan sistem koordinat lokal, mungkin perlu transformasi khusus")

# Periksa kolom WADMKK
print(f"\n4. Memeriksa kolom WADMKK...")
if 'WADMKK' in batas_desa.columns:
    wadmkk_values = batas_desa['WADMKK'].unique()
    print(f"   - Ditemukan {len(wadmkk_values)} nilai WADMKK unik:")
    for wadmkk in sorted(wadmkk_values):
        count = len(batas_desa[batas_desa['WADMKK'] == wadmkk])
        print(f"     • {wadmkk}: {count} fitur")
else:
    print("   ❌ Kolom WADMKK tidak ditemukan!")
    print("   📋 Kolom yang tersedia:", list(batas_desa.columns))

# Lakukan interseksi
print("\n5. Melakukan interseksi...")
try:
    intersected = gpd.overlay(area_tanam, batas_desa, how='intersection')
    print(f"   - Hasil interseksi: {len(intersected)} fitur")
    
    if len(intersected) > 0:
        print("   ✓ Interseksi berhasil!")
        
        # Tampilkan distribusi WADMKK setelah interseksi
        if 'WADMKK' in intersected.columns:
            print(f"\n6. Distribusi WADMKK setelah interseksi:")
            wadmkk_intersect = intersected['WADMKK'].value_counts()
            for wadmkk, count in wadmkk_intersect.items():
                print(f"     • {wadmkk}: {count} fitur")
                
            # Dissolve berdasarkan WADMKK
            print("\n7. Menggabungkan berdasarkan WADMKK...")
            dissolved = intersected.dissolve(by='WADMKK')
            print(f"   - Hasil akhir: {len(dissolved)} wilayah")
            
            # Tampilkan ringkasan
            print("\n8. Ringkasan hasil akhir:")
            for idx, row in dissolved.iterrows():
                try:
                    # Coba hitung luas dalam hektar (asumsi WGS84)
                    # Untuk data WGS84, kita perlu project ke sistem meter untuk perhitungan luas yang akurat
                    temp_geom = gpd.GeoDataFrame([row], crs=dissolved.crs).to_crs('EPSG:32748')  # UTM Zone 48S
                    area_ha = temp_geom.geometry.area.iloc[0] / 10000
                    print(f"     • {idx}: {area_ha:.2f} hektar")
                except:
                    print(f"     • {idx}: (tidak dapat menghitung luas)")
            
            # Simpan hasil
            output_file = "Area_Tanam_Belitung_BelitungTimur_Fixed.shp"
            print(f"\n9. Menyimpan hasil ke: {output_file}")
            dissolved.to_file(output_file)
            
            print("\n=== BERHASIL ===")
            print(f"✓ File output: {output_file}")
            print(f"✓ Total wilayah: {len(dissolved)}")
            print("\nHasil pemrosesan:")
            for idx in dissolved.index:
                print(f"  - Wilayah: {idx}")
                
        else:
            print("   ❌ Kolom WADMKK tidak ada dalam hasil interseksi")
            
    else:
        print("   ❌ Tidak ada interseksi antara area tanam dan batas desa")
        print("   💡 Kemungkinan penyebab:")
        print("      - Data tidak berada di wilayah yang sama")
        print("      - Sistem koordinat masih salah")
        print("      - Area tanam berada di luar batas desa")
        
except Exception as e:
    print(f"   ❌ Error saat interseksi: {e}")

print("\n=== SELESAI ===") 