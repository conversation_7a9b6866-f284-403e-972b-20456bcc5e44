# GIS Processing Tools Project Overview

## Project Purpose
This project provides a suite of GIS processing tools for working with map images, georeferencing, and feature extraction. The tools allow users to extract features from maps based on color, convert them to georeferenced shapefiles, and test alignment between images and shapefiles.

## Key Components

### Image to Shapefile Tools
- **Map Georeferencer** (`map_georeferencer.py`) - Tool for creating georeferencing configurations by marking known points on an image.
- **Shapefile Georeferencer** (`shapefile_georeferencer.py`) - Tool for aligning shapefiles with images.
- **Color Extractor** (`color_extractor.py`, `color_extractor_enhanced.py`) - Tools for extracting features from images based on color ranges.
- **Inverse Color Extractor** (`inverse_color_extractor.py`) - Tool for extracting features by excluding specific colors.
- **Alignment Tester** (`alignment_tester.py`) - Tool for testing the alignment between georeferenced images and shapefiles.

### Utility Scripts
- **Test Alignment** (`test_alignment.py`, `test_alignment_save.py`) - Scripts for testing and saving alignment results.
- **Create Georef Config** (`create_georef_config.py`) - Tool for creating georeferencing configuration files.

## Project Objectives
1. Provide tools for georeferencing raster map images
2. Extract features from maps based on color information
3. Convert extracted features to georeferenced shapefiles
4. Test and verify alignment between images and shapefiles
5. Support both inclusion and exclusion-based color selection for feature extraction

## Current Status
The project includes functional tools for georeferencing, feature extraction, and alignment testing. The latest addition is the inverse color extractor, which allows users to select colors to exclude from the mask (the opposite of the original color extractor functionality).

## Future Development
- Improve the accuracy of georeferencing transformations
- Add support for more complex color selection methods
- Enhance the user interface for better usability
- Add batch processing capabilities
