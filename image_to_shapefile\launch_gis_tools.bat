@echo off
echo Starting GIS Tools Launcher...
echo.

:: Check if python is available in PATH
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Python not found in PATH.
    echo Please install Python or add it to your PATH.
    echo.
    pause
    exit /b 1
)

:: Change to script directory
cd /d "%~dp0"

:: Install required packages if not already installed
echo Checking for required packages...
python -m pip install -r requirements.txt --quiet

:: Launch the application
echo Launching GIS Tools...
python launcher.py

:: If script execution fails
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to launch the application.
    echo Please check the error message above.
    pause
) 