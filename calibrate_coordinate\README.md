# Shapefile Calibrator

A tool for calibrating and aligning shapefiles using a reference shapefile.

## Overview

This tool allows you to calibrate a shapefile by aligning it with a reference shapefile. The application provides a graphical interface that lets you:

1. Load a reference shapefile (base) and a movable shapefile
2. Apply transformations (translation, scaling, rotation) to the movable shapefile
3. Visualize both shapefiles in real-time
4. Save the calibrated shapefile

## Key Features

- **Interactive Calibration**: Drag the shapefile directly with your mouse to position it
- **Multiple Transformation Controls**: Adjust position, scale, and rotation
- **Real-time Visualization**: See changes as you make them
- **Zoom and Pan**: Easily focus on specific areas of the map
- **Automatic CRS Conversion**: Ensures shapefiles use the same coordinate system

## Requirements

- Python 3.7 or higher
- Required Python packages (install via `pip install -r requirements.txt`):
  - geopandas
  - numpy
  - matplotlib
  - shapely
  - pyproj
  - tkinter (usually comes with Python)

## Usage

1. Run the application by double-clicking `launch_calibrator.bat` or running:
   ```
   python shapefile_calibrator.py
   ```

2. In the application:
   - Load your reference shapefile and the shapefile to calibrate
   - Use the mouse to directly drag the shapefile that needs calibration:
     - Left click and drag: Move the shapefile
     - Scroll wheel: Zoom in/out
     - Right click and drag: Pan the view
   - Use the transformation controls for precise adjustments:
     - X/Y Translation: Move the shapefile horizontally/vertically
     - Scale Factor: Resize the shapefile (values > 1 enlarge, values < 1 shrink)
     - Rotation: Rotate the shapefile around its center (in degrees)
   - Observe changes in the visualization panel
   - Save the calibrated shapefile when satisfied

3. The application comes pre-configured with default paths for:
   - Reference shapefile: `D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\HGU REBINMAS\shp_rebinmasjaya.shp`
   - Shapefile to calibrate: `D:\Gawean Rebinmas\Geo  Processing GIS Alat\image_to_shapefile\green_areas.shp`

## Tips for Best Results

- Make small adjustments and observe the visualization
- Use recognizable landmarks or features to help with alignment
- The reference shapefile (blue) remains fixed while the shapefile to calibrate (green) is transformed
- The original position of the shapefile to calibrate is shown with a red dashed line
- Zoom in to make precise adjustments in specific areas 