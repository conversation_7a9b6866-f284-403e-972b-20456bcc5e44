import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
import matplotlib.pyplot as plt

class MapGeoReferencerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Map Georeferencer")
        self.root.geometry("1200x800")
        
        # Initialize variables
        self.image_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\D. Peta Sebaran Areal HCV_page-0001.png"
        self.known_coord = (-2.645815, 107.838156)  # Latitude, Longitude
        self.marked_points = []  # List of (pixel_x, pixel_y, lat, lon)
        self.current_point = None
        self.config_file = "config.json"
        self.scale_factor = 1.0
        self.temp_point = None  # For storing temporary pixel coordinates before adding coordinates
        
        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create left panel for controls
        self.control_frame = ttk.LabelFrame(self.main_frame, text="Controls")
        self.control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # Create right panel for image display
        self.image_frame = ttk.LabelFrame(self.main_frame, text="Map Image")
        self.image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add outer frame for canvas and scrollbars
        self.canvas_frame = ttk.Frame(self.image_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Add canvas for image display
        self.canvas = tk.Canvas(self.canvas_frame, bg="white")
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Add scrollbars
        self.v_scrollbar = ttk.Scrollbar(self.canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.h_scrollbar = ttk.Scrollbar(self.image_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.canvas.configure(xscrollcommand=self.h_scrollbar.set, yscrollcommand=self.v_scrollbar.set)
        
        # Add control buttons
        ttk.Label(self.control_frame, text="Known Coordinate:").pack(anchor=tk.W, padx=5, pady=5)
        
        coord_frame = ttk.Frame(self.control_frame)
        coord_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(coord_frame, text="Lat:").grid(row=0, column=0, padx=2)
        self.lat_var = tk.StringVar(value=str(self.known_coord[0]))
        ttk.Entry(coord_frame, textvariable=self.lat_var, width=12).grid(row=0, column=1, padx=2)
        
        ttk.Label(coord_frame, text="Lon:").grid(row=0, column=2, padx=2)
        self.lon_var = tk.StringVar(value=str(self.known_coord[1]))
        ttk.Entry(coord_frame, textvariable=self.lon_var, width=12).grid(row=0, column=3, padx=2)
        
        # Add buttons
        ttk.Button(self.control_frame, text="Load Image", command=self.load_image).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(self.control_frame, text="Mark Known Point", command=self.start_marking_known_point).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(self.control_frame, text="Add Specific Point", command=self.start_marking_specific_point).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(self.control_frame, text="Save to JSON", command=self.save_to_json).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(self.control_frame, text="Export to Shapefile", command=self.export_to_shapefile).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(self.control_frame, text="Clear All Points", command=self.clear_all_points).pack(fill=tk.X, padx=5, pady=5)
        
        # Add zoom controls
        zoom_frame = ttk.LabelFrame(self.control_frame, text="Zoom")
        zoom_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(zoom_frame, text="+", command=self.zoom_in).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        ttk.Button(zoom_frame, text="-", command=self.zoom_out).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5, pady=5)
        
        # Add points list
        points_frame = ttk.LabelFrame(self.control_frame, text="Marked Points")
        points_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.points_listbox = tk.Listbox(points_frame)
        self.points_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add coordinate entry dialog frame (initially hidden)
        self.coord_dialog = ttk.LabelFrame(self.control_frame, text="Enter Coordinates")
        
        coord_entry_frame = ttk.Frame(self.coord_dialog)
        coord_entry_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(coord_entry_frame, text="Lat:").grid(row=0, column=0, padx=2)
        self.point_lat_var = tk.StringVar()
        ttk.Entry(coord_entry_frame, textvariable=self.point_lat_var, width=12).grid(row=0, column=1, padx=2)
        
        ttk.Label(coord_entry_frame, text="Lon:").grid(row=0, column=2, padx=2)
        self.point_lon_var = tk.StringVar()
        ttk.Entry(coord_entry_frame, textvariable=self.point_lon_var, width=12).grid(row=0, column=3, padx=2)
        
        ttk.Button(self.coord_dialog, text="Add Point", command=self.add_specific_point).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(self.coord_dialog, text="Cancel", command=self.cancel_specific_point).pack(fill=tk.X, padx=5, pady=5)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<Motion>", self.on_canvas_move)
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)  # For Windows
        self.canvas.bind("<Button-4>", self.on_mousewheel)  # For Linux scroll up
        self.canvas.bind("<Button-5>", self.on_mousewheel)  # For Linux scroll down
        
        # Load the image
        self.load_initial_image()
        
        # Set initial status
        self.status_var.set("Ready. Load an image or mark the known coordinate.")
        
        # Mode flags
        self.marking_known_point = False
        self.marking_specific_point = False
        self.marking_more_points = False
    
    def load_initial_image(self):
        if os.path.exists(self.image_path):
            self.load_image_from_path(self.image_path)
        else:
            self.status_var.set(f"Image not found: {self.image_path}")
    
    def load_image(self):
        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.tif;*.tiff"), ("All files", "*.*")]
        )
        if file_path:
            self.image_path = file_path
            self.load_image_from_path(file_path)
    
    def load_image_from_path(self, path):
        try:
            self.original_image = Image.open(path)
            self.update_image()
            self.status_var.set(f"Loaded image: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.status_var.set(f"Error loading image: {str(e)}")
    
    def update_image(self):
        if hasattr(self, 'original_image'):
            # Resize image based on scale factor
            width = int(self.original_image.width * self.scale_factor)
            height = int(self.original_image.height * self.scale_factor)
            resized_image = self.original_image.resize((width, height), Image.LANCZOS)
            
            # Convert to PhotoImage
            self.tk_image = ImageTk.PhotoImage(resized_image)
            
            # Update canvas
            self.canvas.delete("all")
            self.canvas_image = self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)
            self.canvas.config(scrollregion=self.canvas.bbox(tk.ALL))
            
            # Redraw all points
            self.redraw_points()
    
    def redraw_points(self):
        if hasattr(self, 'original_image'):
            self.canvas.delete("point")
            for i, (px, py, lat, lon) in enumerate(self.marked_points):
                # Scale the pixel coordinates
                scaled_x = px * self.scale_factor
                scaled_y = py * self.scale_factor
                
                # Draw point
                point_id = self.canvas.create_oval(
                    scaled_x - 5, scaled_y - 5, 
                    scaled_x + 5, scaled_y + 5, 
                    fill="red", outline="black", tags="point"
                )
                
                # Draw label
                label_id = self.canvas.create_text(
                    scaled_x, scaled_y - 10, 
                    text=f"P{i+1}", 
                    fill="black", font=("Arial", 10, "bold"), 
                    tags="point"
                )
    
    def start_marking_known_point(self):
        self.marking_known_point = True
        self.marking_specific_point = False
        self.marking_more_points = False
        self.status_var.set("Click on the image to mark the known coordinate point.")
    
    def start_marking_specific_point(self):
        if not hasattr(self, 'original_image'):
            messagebox.showwarning("Warning", "Please load an image first.")
            return
            
        self.marking_known_point = False
        self.marking_specific_point = True
        self.marking_more_points = False
        self.temp_point = None
        self.status_var.set("Click on the image to select a point, then enter its coordinates.")
    
    def add_specific_point(self):
        if not self.temp_point:
            messagebox.showwarning("Warning", "Please click on the image first to select a point.")
            return
            
        try:
            lat = float(self.point_lat_var.get())
            lon = float(self.point_lon_var.get())
        except ValueError:
            messagebox.showerror("Error", "Invalid coordinate values. Please enter valid numbers.")
            return
            
        original_x, original_y = self.temp_point
        
        # Add the point with user-specified coordinates
        point_index = len(self.marked_points) + 1
        self.marked_points.append((original_x, original_y, lat, lon))
        self.points_listbox.insert(tk.END, f"P{point_index}: ({original_x}, {original_y}) -> ({lat:.6f}, {lon:.6f})")
        
        # Update display
        self.redraw_points()
        self.status_var.set(f"Point {point_index} added at pixel ({original_x}, {original_y}) -> geo ({lat:.6f}, {lon:.6f})")
        
        # Hide coordinate entry dialog
        self.coord_dialog.pack_forget()
        self.temp_point = None
        self.marking_specific_point = False
    
    def cancel_specific_point(self):
        self.coord_dialog.pack_forget()
        self.temp_point = None
        self.marking_specific_point = False
        self.status_var.set("Point addition cancelled.")
    
    def start_marking_more_points(self):
        if not self.marked_points:
            messagebox.showwarning("Warning", "You must mark at least one point first.")
            return
        
        self.marking_known_point = False
        self.marking_specific_point = False
        self.marking_more_points = True
        self.status_var.set("Click on the image to mark additional points.")
    
    def on_canvas_click(self, event):
        if not hasattr(self, 'original_image'):
            messagebox.showwarning("Warning", "Please load an image first.")
            return
        
        # Get canvas coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)
        
        if self.marking_known_point:
            # Clear existing points
            self.marked_points = []
            self.points_listbox.delete(0, tk.END)
            
            # Get coordinates from entry fields
            try:
                lat = float(self.lat_var.get())
                lon = float(self.lon_var.get())
                self.known_coord = (lat, lon)
            except ValueError:
                messagebox.showerror("Error", "Invalid coordinate values. Please enter valid numbers.")
                return
            
            # Add the known point
            self.marked_points.append((original_x, original_y, lat, lon))
            self.points_listbox.insert(tk.END, f"P1: ({original_x}, {original_y}) -> ({lat:.6f}, {lon:.6f})")
            
            # Update display
            self.redraw_points()
            self.marking_known_point = False
            self.status_var.set(f"Known point marked at pixel ({original_x}, {original_y}) -> geo ({lat:.6f}, {lon:.6f})")
        
        elif self.marking_specific_point:
            # Store the pixel coordinates temporarily
            self.temp_point = (original_x, original_y)
            
            # Show coordinate entry dialog
            self.point_lat_var.set("")
            self.point_lon_var.set("")
            self.coord_dialog.pack(fill=tk.X, padx=5, pady=5, after=self.points_listbox.master)
            
            self.status_var.set(f"Selected pixel ({original_x}, {original_y}). Now enter the geographic coordinates.")
        
        elif self.marking_more_points:
            # Show dialog to enter coordinates for this point
            self.temp_point = (original_x, original_y)
            
            # Show coordinate entry dialog
            self.point_lat_var.set("")
            self.point_lon_var.set("")
            self.coord_dialog.pack(fill=tk.X, padx=5, pady=5, after=self.points_listbox.master)
            
            self.status_var.set(f"Selected pixel ({original_x}, {original_y}). Now enter the geographic coordinates.")
    
    def on_canvas_move(self, event):
        if hasattr(self, 'original_image'):
            # Get canvas coordinates
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)
            
            # Convert to original image coordinates
            original_x = int(canvas_x / self.scale_factor)
            original_y = int(canvas_y / self.scale_factor)
            
            # Update status bar with pixel coordinates
            self.status_var.set(f"Pixel: ({original_x}, {original_y})")
    
    def on_mousewheel(self, event):
        # Handle mouse wheel for zooming
        if event.num == 4 or event.delta > 0:  # Scroll up
            self.zoom_in()
        elif event.num == 5 or event.delta < 0:  # Scroll down
            self.zoom_out()
    
    def save_to_json(self):
        if not self.marked_points:
            messagebox.showwarning("Warning", "No points to save.")
            return
        
        try:
            # Create data structure
            data = {
                "image_path": self.image_path,
                "image_width": self.original_image.width,
                "image_height": self.original_image.height,
                "points": []
            }
            
            for px, py, lat, lon in self.marked_points:
                data["points"].append({
                    "pixel_x": px,
                    "pixel_y": py,
                    "latitude": lat,
                    "longitude": lon
                })
            
            # Save to file
            file_path = filedialog.asksaveasfilename(
                title="Save JSON File",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialfile="map_georef_config.json"
            )
            
            if file_path:
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=4)
                self.status_var.set(f"Saved configuration to {file_path}")
                messagebox.showinfo("Success", f"Configuration saved to {file_path}")
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save JSON: {str(e)}")
            self.status_var.set(f"Error saving JSON: {str(e)}")
    
    def export_to_shapefile(self):
        if not self.marked_points:
            messagebox.showwarning("Warning", "No points to export.")
            return
        
        try:
            # Create GeoDataFrame
            points = []
            data = {"id": [], "pixel_x": [], "pixel_y": [], "latitude": [], "longitude": []}
            
            for i, (px, py, lat, lon) in enumerate(self.marked_points):
                points.append(Point(lon, lat))  # GeoDataFrame expects (lon, lat) order
                data["id"].append(i + 1)
                data["pixel_x"].append(px)
                data["pixel_y"].append(py)
                data["latitude"].append(lat)
                data["longitude"].append(lon)
            
            gdf = gpd.GeoDataFrame(data, geometry=points, crs="EPSG:4326")
            
            # Save to file
            file_path = filedialog.asksaveasfilename(
                title="Save Shapefile",
                defaultextension=".shp",
                filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
                initialfile="map_points.shp"
            )
            
            if file_path:
                gdf.to_file(file_path)
                self.status_var.set(f"Exported points to {file_path}")
                messagebox.showinfo("Success", f"Points exported to {file_path}")
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export shapefile: {str(e)}")
            self.status_var.set(f"Error exporting shapefile: {str(e)}")
    
    def clear_all_points(self):
        if messagebox.askyesno("Confirm", "Are you sure you want to clear all marked points?"):
            self.marked_points = []
            self.points_listbox.delete(0, tk.END)
            self.redraw_points()
            self.status_var.set("All points cleared.")
    
    def zoom_in(self):
        self.scale_factor *= 1.2
        self.update_image()
    
    def zoom_out(self):
        self.scale_factor /= 1.2
        self.update_image()

def main():
    root = tk.Tk()
    app = MapGeoReferencerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
