@echo off
echo DWG to Shapefile Converter - GUI Mode
echo ====================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Install requirements if needed
echo Installing required packages...
pip install -r requirements.txt

echo.
echo Starting GUI...

REM Run the converter with GUI
python main.py --gui
