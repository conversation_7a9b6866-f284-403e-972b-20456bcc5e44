#!/usr/bin/env python3
"""
Test script for SPH Report Generator
Creates sample data and demonstrates the report generation functionality
"""

import pandas as pd
import numpy as np
from sph_report_generator import SPHReportGenerator
import os

def create_sample_sph_data():
    """Create sample SPH data matching the user's format"""
    sample_data = [
        # Boundary records with HCV_Catego = 0
        {
            'Id': 0,
            'Divisi': 'DIVISI ARE B 2',
            'SUB_DIVISI': 'SUB DIVISI AIR BANGEK',
            'BLOK': 'P 98 / 03',
            'LUAS_AUTO': 52.03,
            'Jumlah_Poh': 6914,
            'HCV_Catego': 0,
            'ID_Feature': 'Boundary-60',
            'luas_aut_1': 52.03,
            'SPH': 133.89,
            'contained': '',
            'total_incl': 0,
            'luas_netto': 52.03
        },
        {
            'Id': 0,
            'Divisi': 'DIVISI ARE B 2',
            'SUB_DIVISI': 'SUB DIVISI AIR BANGEK',
            'BLOK': 'P 98 / 02',
            'LUAS_AUTO': 73.01,
            'Ju<PERSON>lah_Poh': 9439,
            'HCV_Catego': 0,
            'ID_Feature': 'Boundary-61',
            'luas_aut_1': 73.05,
            'SPH': 137.94,
            'contained': 'Inclave-12, Inclave-13, Inclave-14, Inclave-15',
            'total_incl': 4390000000000000,  # This seems to be an error in original data
            'luas_netto': 68.62
        },
        # Inclave records (should be excluded from main calculation but used for inclave area)
        {
            'Id': 0,
            'Divisi': 'DIVISI ARE B 2',
            'SUB_DIVISI': 'SUB DIVISI AIR BANGEK',
            'BLOK': 'P 98 / 02',
            'LUAS_AUTO': 73.01,
            'Jumlah_Poh': '',
            'HCV_Catego': 1,
            'ID_Feature': 'Inclave-12',
            'luas_aut_1': 0.69,
            'SPH': '',
            'contained': '',
            'total_incl': '',
            'luas_netto': ''
        },
        {
            'Id': 0,
            'Divisi': 'DIVISI ARE B 2',
            'SUB_DIVISI': 'SUB DIVISI AIR BANGEK',
            'BLOK': 'P 98 / 02',
            'LUAS_AUTO': 73.01,
            'Jumlah_Poh': '',
            'HCV_Catego': 1,
            'ID_Feature': 'Inclave-13',
            'luas_aut_1': 0.66,
            'SPH': '',
            'contained': '',
            'total_incl': '',
            'luas_netto': ''
        },
        {
            'Id': 0,
            'Divisi': 'DIVISI ARE B 2',
            'SUB_DIVISI': 'SUB DIVISI AIR BANGEK',
            'BLOK': 'P 98 / 02',
            'LUAS_AUTO': 73.01,
            'Jumlah_Poh': '',
            'HCV_Catego': 1,
            'ID_Feature': 'Inclave-14',
            'luas_aut_1': 2.79,
            'SPH': '',
            'contained': '',
            'total_incl': '',
            'luas_netto': ''
        },
        # Additional sample data for different divisions
        {
            'Id': 1,
            'Divisi': 'DIVISI ARE A 1',
            'SUB_DIVISI': 'SUB DIVISI SUNGAI DERAS',
            'BLOK': 'P 97 / 01',
            'LUAS_AUTO': 65.50,
            'Jumlah_Poh': 8200,
            'HCV_Catego': 0,
            'ID_Feature': 'Boundary-62',
            'luas_aut_1': 65.50,
            'SPH': 125.19,
            'contained': '',
            'total_incl': 0,
            'luas_netto': 65.50
        },
        {
            'Id': 2,
            'Divisi': 'DIVISI ARE A 1',
            'SUB_DIVISI': 'SUB DIVISI SUNGAI DERAS',
            'BLOK': 'P 97 / 02',
            'LUAS_AUTO': 48.75,
            'Jumlah_Poh': 6100,
            'HCV_Catego': 0,
            'ID_Feature': 'Boundary-63',
            'luas_aut_1': 48.75,
            'SPH': 125.13,
            'contained': 'Inclave-15',
            'total_incl': 0,
            'luas_netto': 47.50
        },
        # Inclave for the above
        {
            'Id': 2,
            'Divisi': 'DIVISI ARE A 1',
            'SUB_DIVISI': 'SUB DIVISI SUNGAI DERAS',
            'BLOK': 'P 97 / 02',
            'LUAS_AUTO': 48.75,
            'Jumlah_Poh': '',
            'HCV_Catego': 1,
            'ID_Feature': 'Inclave-15',
            'luas_aut_1': 1.25,
            'SPH': '',
            'contained': '',
            'total_incl': '',
            'luas_netto': ''
        }
    ]
    
    df = pd.DataFrame(sample_data)
    return df

def test_sph_report_generator():
    """Test the SPH report generator with sample data"""
    print("Creating sample SPH data...")
    
    # Create sample data
    sample_df = create_sample_sph_data()
    
    # Save sample data to CSV for testing
    sample_file = "sample_sph_data.csv"
    sample_df.to_csv(sample_file, index=False)
    print(f"Sample data saved to: {sample_file}")
    
    # Display sample data info
    print(f"\nSample data contains {len(sample_df)} records")
    print(f"Columns: {list(sample_df.columns)}")
    print("\nFirst few records:")
    print(sample_df.head())
    
    print(f"\nID_Feature values: {sample_df['ID_Feature'].unique().tolist()}")
    print(f"HCV_Catego values: {sample_df['HCV_Catego'].unique().tolist()}")
    
    # Test the report generator
    print("\n" + "="*50)
    print("Testing SPH Report Generator...")
    print("="*50)
    
    try:
        generator = SPHReportGenerator()
        
        # Generate summary report
        summary = generator.generate_summary_report(sample_df)
        
        print("\nGenerated Summary Report:")
        print(summary)
        
        # Export to Excel
        output_file = generator.export_to_excel(summary, sample_df, "test_sph_report.xlsx")
        
        print(f"\n✓ Test completed successfully!")
        print(f"✓ Report exported to: {output_file}")
        print(f"✓ Summary contains {len(summary)} grouped records")
        
        # Display summary statistics
        print(f"\nSummary Statistics:")
        print(f"- Total Area (Ha): {summary['Total_Luas_Auto_Ha'].sum():.2f}")
        print(f"- Total Inclave Area (Ha): {summary['Total_Luas_Inclave_Ha'].sum():.2f}")
        print(f"- Total Net Area (Ha): {summary['Total_Luas_Netto_Ha'].sum():.2f}")
        print(f"- Total Trees: {summary['Total_Jumlah_Pohon'].sum():,}")
        print(f"- Average SPH: {summary['Rata_rata_SPH'].mean():.2f}")
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        raise
    
    finally:
        # Clean up test files
        if os.path.exists(sample_file):
            os.remove(sample_file)
            print(f"\nCleaned up test file: {sample_file}")

if __name__ == "__main__":
    test_sph_report_generator() 