#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Unplanted Area Report Generator

This script runs the generate_unplanted_report.py script to generate a report
from a shapefile containing boundary and unplanted area data. It provides a simple
command-line interface to select the input shapefile and specify the output files.

Author: AI Assistant
Date: 2025-05-22
"""

import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox
from generate_unplanted_report import UnplantedReportGenerator, logger, REPORTLAB_AVAILABLE

def main():
    """Main function to run the unplanted area report generator."""
    # Create a simple GUI to select the input file
    root = tk.Tk()
    root.title("Generate Unplanted Area Report")
    root.geometry("600x400")

    # Create a label with instructions
    label = tk.Label(root, text="This tool generates a report from a shapefile containing\n"
                              "boundary and unplanted area data. The report includes maps\n"
                              "showing the positions of unplanted areas, their sizes, and\n"
                              "statistics grouped by division and subdivision.",
                    font=("Arial", 12))
    label.pack(pady=20)

    # Create a frame for the input file selection
    input_frame = tk.Frame(root)
    input_frame.pack(pady=10, fill=tk.X, padx=20)

    input_label = tk.Label(input_frame, text="Input Shapefile:", font=("Arial", 10))
    input_label.pack(side=tk.LEFT, padx=5)

    input_var = tk.StringVar()
    input_entry = tk.Entry(input_frame, textvariable=input_var, width=50)
    input_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

    def browse_input():
        filename = filedialog.askopenfilename(
            title="Select Shapefile for Report",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            input_var.set(filename)

            # Set default output filenames in the Unplanted Report folder
            basename = os.path.splitext(os.path.basename(filename))[0]
            unplanted_report_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Unplanted Report")
            os.makedirs(unplanted_report_dir, exist_ok=True)
            html_var.set(os.path.join(unplanted_report_dir, f"{basename}_unplanted_report.html"))
            pdf_var.set(os.path.join(unplanted_report_dir, f"{basename}_unplanted_report.pdf"))

    browse_button = tk.Button(input_frame, text="Browse...", command=browse_input)
    browse_button.pack(side=tk.LEFT, padx=5)

    # Create a frame for the HTML output file selection
    html_frame = tk.Frame(root)
    html_frame.pack(pady=10, fill=tk.X, padx=20)

    html_label = tk.Label(html_frame, text="HTML Output:", font=("Arial", 10))
    html_label.pack(side=tk.LEFT, padx=5)

    html_var = tk.StringVar()
    html_entry = tk.Entry(html_frame, textvariable=html_var, width=50)
    html_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

    def browse_html():
        # Create the Unplanted Report folder if it doesn't exist
        unplanted_report_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Unplanted Report")
        os.makedirs(unplanted_report_dir, exist_ok=True)

        filename = filedialog.asksaveasfilename(
            title="Save HTML Report As",
            filetypes=[("HTML Files", "*.html"), ("All files", "*.*")],
            defaultextension=".html",
            initialdir=unplanted_report_dir
        )
        if filename:
            # Make sure the file is saved in the Unplanted Report folder
            basename = os.path.basename(filename)
            html_path = os.path.join(unplanted_report_dir, basename)
            html_var.set(html_path)

            # Update PDF filename to match
            pdf_var.set(os.path.splitext(html_path)[0] + ".pdf")

    html_button = tk.Button(html_frame, text="Browse...", command=browse_html)
    html_button.pack(side=tk.LEFT, padx=5)

    # Create a frame for the PDF output file selection
    pdf_frame = tk.Frame(root)
    pdf_frame.pack(pady=10, fill=tk.X, padx=20)

    pdf_label = tk.Label(pdf_frame, text="PDF Output:", font=("Arial", 10))
    pdf_label.pack(side=tk.LEFT, padx=5)

    pdf_var = tk.StringVar()
    pdf_entry = tk.Entry(pdf_frame, textvariable=pdf_var, width=50)
    pdf_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

    def browse_pdf():
        # Create the Unplanted Report folder if it doesn't exist
        unplanted_report_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Unplanted Report")
        os.makedirs(unplanted_report_dir, exist_ok=True)

        filename = filedialog.asksaveasfilename(
            title="Save PDF Report As",
            filetypes=[("PDF Files", "*.pdf"), ("All files", "*.*")],
            defaultextension=".pdf",
            initialdir=unplanted_report_dir
        )
        if filename:
            # Make sure the file is saved in the Unplanted Report folder
            basename = os.path.basename(filename)
            pdf_path = os.path.join(unplanted_report_dir, basename)
            pdf_var.set(pdf_path)

    pdf_button = tk.Button(pdf_frame, text="Browse...", command=browse_pdf)
    pdf_button.pack(side=tk.LEFT, padx=5)

    # Create a status label
    status_label = tk.Label(root, text="Ready to generate report.", font=("Arial", 10))
    status_label.pack(pady=20)

    # Function to handle the report generation
    def generate_report():
        input_file = input_var.get()
        output_html = html_var.get()
        output_pdf = pdf_var.get()

        if not input_file:
            messagebox.showerror("Error", "No input file selected.")
            return

        if not output_html:
            messagebox.showerror("Error", "No HTML output file specified.")
            return

        # Update status
        status_label.config(text=f"Processing {os.path.basename(input_file)}...")
        root.update()

        # Create the generator and process the shapefile
        generator = UnplantedReportGenerator(input_file)

        if generator.load_shapefile():
            status_label.config(text="Analyzing data...")
            root.update()

            if generator.analyze_data():
                status_label.config(text="Generating HTML report...")
                root.update()

                html_saved = generator.generate_html_report(output_html)

                if REPORTLAB_AVAILABLE and output_pdf:
                    status_label.config(text="Generating PDF report...")
                    root.update()

                    pdf_saved = generator.generate_pdf_report(output_pdf)
                    if pdf_saved:
                        logger.info(f"PDF report saved successfully")
                    else:
                        messagebox.showwarning("Warning", "PDF report generation failed.")
                else:
                    if not REPORTLAB_AVAILABLE:
                        messagebox.showwarning("Warning", "ReportLab not available. PDF report was not generated.")

                if html_saved:
                    status_label.config(text=f"Report generation completed successfully!\n"
                                           f"HTML report saved to {os.path.basename(output_html)}")
                    messagebox.showinfo("Success", "Report generation completed successfully!")
                else:
                    status_label.config(text="Failed to generate HTML report. Check the log for details.")
                    messagebox.showerror("Error", "Failed to generate HTML report. Check the log for details.")
            else:
                status_label.config(text="Failed to analyze the shapefile data. Check the log for details.")
                messagebox.showerror("Error", "Failed to analyze the shapefile data. Check the log for details.")
        else:
            status_label.config(text="Failed to load the shapefile. Check the log for details.")
            messagebox.showerror("Error", "Failed to load the shapefile. Check the log for details.")

    # Create buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)

    generate_button = tk.Button(button_frame, text="Generate Report", command=generate_report,
                              font=("Arial", 11), padx=10, pady=5)
    generate_button.pack(side=tk.LEFT, padx=10)

    exit_button = tk.Button(button_frame, text="Exit", command=root.destroy,
                           font=("Arial", 11), padx=10, pady=5)
    exit_button.pack(side=tk.LEFT, padx=10)

    # Run the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
