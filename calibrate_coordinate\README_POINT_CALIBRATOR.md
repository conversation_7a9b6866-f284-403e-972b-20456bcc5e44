# Point-Based Shapefile Calibrator

A tool for calibrating shapefiles using known reference points.

## Overview

This tool allows you to calibrate a shapefile by specifying a reference point in the current shapefile and where that point should be in the target coordinate system. The application:

1. Calculates the exact shift needed to align the points
2. Applies this shift to the entire shapefile
3. Visualizes the original, reference, and transformed shapefiles

## Key Features

- **Point-Based Calibration**: Precisely calculate the correct position shift
- **Coordinate System Support**: Automatically handles coordinate system differences
- **Visual Verification**: See the original points, target points, and the transformation result
- **Simple Workflow**: Just enter the coordinates, calculate the shift, and apply
- **Advanced Transformation Methods**: Choose between methods for consistent results across all features
- **Transformation Debugging**: View detailed information about applied transformations

## Requirements

- Python 3.7 or higher
- Required Python packages (install via `pip install -r requirements.txt`):
  - geopandas
  - numpy
  - matplotlib
  - shapely
  - pyproj
  - tkinter (usually comes with Python)

## Usage

1. Run the application by double-clicking `launch_point_calibrator.bat` or running:
   ```
   python point_based_calibrator.py
   ```

2. In the application:
   - Load your reference shapefile and the shapefile to calibrate
   - Enter the current coordinates of a known point in your shapefile
   - Enter the target coordinates where that point should be
   - **Select your transformation method**:
     - **Matrix Transformation**: Recommended for consistent results across all features
     - **Shapely Affine**: Traditional method (might have inconsistencies with complex geometries)
   - Click "Calculate Shift" to determine the necessary adjustment
   - Click "Apply Shift" to transform the shapefile
   - Click "Save Calibrated Shapefile" when satisfied

3. The application provides clear information about:
   - Horizontal shift (+ means right, - means left)
   - Vertical shift (+ means up, - means down)
   - Total distance between the original and target points

4. **NEW! Transformation Verification**:
   - Click "View Transformation Details" to see a detailed report
   - See how each geometry type has been transformed
   - Verify that the shift has been consistently applied to all features
   - Compare actual vs. expected shift values to identify any discrepancies

5. The application comes pre-configured with:
   - Default reference shapefile: `D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\HGU REBINMAS\shp_rebinmasjaya.shp`
   - Default shapefile to calibrate: `D:\Gawean Rebinmas\Geo  Processing GIS Alat\image_to_shapefile\green_areas.shp`
   - Default point coordinates from the example (X: 824479.733, Y: 9684076.776 → X: 824105.400, Y: 9684031.784)

## Troubleshooting Transformation Issues

If you notice inconsistencies in how the transformation is applied:

1. Try the "Matrix Transformation" method which applies shifts directly to the raw coordinates
2. Use the "View Transformation Details" option to check if all features are being transformed correctly
3. Examine the feature information panel to understand the types and number of geometries being transformed
4. For complex shapefiles with multiple geometry types, verify each type is being transformed correctly

## Workflow

1. **Load Shapefiles**: Load your reference and target shapefiles
2. **Enter Points**: Input current and target coordinates for a known reference point
3. **Choose Method**: Select the appropriate transformation method for your needs
4. **Calculate Shift**: Determine the exact transformation needed
5. **Verify Visually**: Check the preview to confirm the transformation looks correct
6. **Verify Consistency**: Use the transformation details viewer to ensure consistent application
7. **Apply Shift**: Apply the calculated transformation to the shapefile
8. **Save Result**: Save the calibrated shapefile to a new file 