# System Patterns

## System Architecture

The GIS Processing Tools project follows a modular architecture with three distinct applications that share common design patterns:

```mermaid
flowchart TD
    subgraph "Image to Shapefile"
    A1[Map Georeferencer] --> A2[Color Extractor]
    A2 --> A3[Shapefile Export]
    end
    
    subgraph "Inclave Extractor"
    B1[Shapefile Input] --> B2[Feature Filter]
    B2 --> B3[Shapefile Export]
    end
    
    subgraph "Unplanted Report"
    C1[Shapefile Input] --> C2[Data Analysis]
    C2 --> C3[Report Generation]
    C3 --> C4[HTML/PDF Output]
    end
    
    A3 -.-> B1
    B3 -.-> C1
```

## Key Technical Decisions

1. **Python-Based Tools**: All applications are written in Python for cross-platform compatibility and access to strong GIS libraries
2. **Tkinter for GUI**: Simple, consistent user interfaces built with <PERSON>kinter for all tools
3. **Common GIS Libraries**: Standardized use of GeoPandas, Shapely, and other geospatial libraries
4. **File-Based Data Exchange**: Applications communicate through standard GIS file formats (shapefiles)
5. **Standalone Applications**: Each tool functions independently but can be part of a workflow

## Design Patterns

### MVC-Like Organization
Each application follows a loose Model-View-Controller pattern:
- **Model**: GeoPandas DataFrame/GeoDataFrame for data
- **View**: Tkinter GUI for user interaction
- **Controller**: Application class handling logic and connecting view to model

### Class-Based Structure
- Main application functionality encapsulated in classes
- Separation of GUI elements from processing logic
- Independent main() function to allow both command-line and GUI usage

### Event-Driven UI
- User interactions trigger specific event handlers
- Processing functions are executed in response to user actions
- Status updates provide feedback to users

## Component Relationships

### Map Georeferencer & Color Extractor
- Georeferencer establishes coordinate system for images
- Color Extractor uses georeferencing data to create spatially accurate features
- Both share configuration data in JSON format

### Inclave Extractor
- Simple filter for shapefiles based on attribute criteria
- Extracts HCV category 1 features (inclaves)
- Processes output from Color Extractor or other sources

### Unplanted Report Generator
- More complex analysis tool
- Groups data by administrative divisions
- Generates statistics and visualizations
- Outputs to HTML and PDF formats

## Processing Pipelines

```mermaid
flowchart LR
    subgraph "Image Processing Pipeline"
    I1[Load Image] --> I2[Georeference Points]
    I2 --> I3[Extract Colors]
    I3 --> I4[Create Polygons]
    I4 --> I5[Export Shapefile]
    end
    
    subgraph "Reporting Pipeline"
    R1[Load Shapefile] --> R2[Analyze Boundaries/Inclaves]
    R2 --> R3[Generate Statistics]
    R3 --> R4[Create Visualizations]
    R4 --> R5[Compile Report]
    end
``` 