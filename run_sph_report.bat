@echo off
echo SPH Report Generator
echo ===================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Install required packages
echo Installing required packages...
pip install -r requirements.txt

REM Run the SPH report generator
echo.
echo Starting SPH Report Generator...
python sph_report_generator.py

echo.
echo Program finished.
pause 