import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, Scale
import numpy as np
import cv2
from PIL import Image, ImageTk
import geopandas as gpd
from shapely.geometry import Polygon, MultiPolygon
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.colors as mcolors
from collections import deque

class ColorExtractorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Color Extractor")
        self.root.geometry("1400x900")

        # Initialize variables
        self.image_path = None
        self.config_path = None
        self.original_image = None
        self.cv_image = None
        self.mask = None
        self.contours = None
        self.polygons = []
        self.georef_data = None
        self.scale_factor = 1.0

        # HSV range variables
        self.h_min = tk.IntVar(value=40)
        self.h_max = tk.IntVar(value=80)
        self.s_min = tk.IntVar(value=40)
        self.s_max = tk.IntVar(value=255)
        self.v_min = tk.IntVar(value=40)
        self.v_max = tk.IntVar(value=255)

        # Polygon simplification variables
        self.epsilon_factor = tk.DoubleVar(value=0.001)
        self.min_area = tk.IntVar(value=100)

        # Color sampling variables
        self.color_samples = []  # List of (h, s, v) tuples
        self.sample_tolerance = tk.IntVar(value=10)  # Tolerance for color matching
        self.sampling_mode = False  # Flag to indicate if we're in sampling mode
        self.combined_mask = None  # Combined mask from all color samples

        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a frame with scrollbar for the left panel
        self.control_outer_frame = ttk.Frame(self.main_frame)
        self.control_outer_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Create a canvas with scrollbar
        self.control_canvas = tk.Canvas(self.control_outer_frame, width=280)
        self.control_scrollbar = ttk.Scrollbar(self.control_outer_frame, orient="vertical", command=self.control_canvas.yview)

        # Pack the scrollbar and canvas
        self.control_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.control_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.control_canvas.configure(yscrollcommand=self.control_scrollbar.set)

        # Create the frame for controls inside the canvas
        self.control_frame = ttk.Frame(self.control_canvas)
        self.control_window = self.control_canvas.create_window((0, 0), window=self.control_frame, anchor="nw")

        # Configure the canvas to resize the window when the frame changes size
        def configure_control_frame(event):
            self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
            self.control_canvas.itemconfig(self.control_window, width=event.width)

        self.control_frame.bind("<Configure>", configure_control_frame)

        # Bind mousewheel to scroll
        self.control_canvas.bind_all("<MouseWheel>", self.on_mousewheel)

        # Create right panel for image display
        self.image_frame = ttk.LabelFrame(self.main_frame, text="Image Preview")
        self.image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a color preview box for sampling
        self.color_preview = tk.Canvas(self.root, width=30, height=30, bg="white", highlightthickness=1, highlightbackground="black")
        self.color_preview.place(x=-100, y=-100)  # Place it off-screen initially

        # Create notebook for different views
        self.notebook = ttk.Notebook(self.image_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Original image tab
        self.original_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.original_tab, text="Original Image")

        # Mask preview tab
        self.mask_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.mask_tab, text="Color Mask")

        # Contours preview tab
        self.contour_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.contour_tab, text="Contours")

        # Setup scrollable canvases for each tab
        self.setup_scrollable_canvas(self.original_tab, "original_canvas")
        self.setup_scrollable_canvas(self.mask_tab, "mask_canvas")
        self.setup_scrollable_canvas(self.contour_tab, "contour_canvas")

        # Bind click event to original canvas for color sampling
        self.original_canvas.bind("<Button-1>", self.on_canvas_click)

        # Add status label for mouse position and color info
        self.color_info_var = tk.StringVar()
        self.color_info_label = ttk.Label(self.original_tab, textvariable=self.color_info_var,
                                         relief=tk.SUNKEN, anchor=tk.W)
        self.color_info_label.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind mouse movement to show color info
        self.original_canvas.bind("<Motion>", self.on_canvas_move)

        # Add control elements
        self.create_control_panel()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Set initial status
        self.status_var.set("Ready. Load an image and georeferencing config to begin.")

    def setup_scrollable_canvas(self, parent, canvas_name):
        """Create a scrollable canvas with both horizontal and vertical scrollbars."""
        # Create a frame to hold the canvas and scrollbars
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True)

        # Create canvas
        canvas = tk.Canvas(frame, bg="white")
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add scrollbars
        h_scrollbar = ttk.Scrollbar(frame, orient=tk.HORIZONTAL, command=canvas.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        v_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=canvas.yview)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure canvas
        canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # Store canvas as an attribute of the class
        setattr(self, canvas_name, canvas)

        return canvas

    def create_control_panel(self):
        # Add a title for the control panel
        title_label = ttk.Label(self.control_frame, text="Color Extractor Controls", font=("Arial", 12, "bold"))
        title_label.pack(fill=tk.X, padx=5, pady=5)

        # File controls
        file_frame = ttk.LabelFrame(self.control_frame, text="Files")
        file_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(file_frame, text="Load Image", command=self.load_image).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(file_frame, text="Load Georef Config", command=self.load_config).pack(fill=tk.X, padx=5, pady=5)

        # Color sampling controls
        sampling_frame = ttk.LabelFrame(self.control_frame, text="Color Sampling")
        sampling_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(sampling_frame, text="Start Color Sampling",
                  command=self.start_color_sampling).pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(sampling_frame, text="Sample Tolerance:").pack(anchor=tk.W, padx=5, pady=2)
        Scale(sampling_frame, from_=1, to=50, orient=tk.HORIZONTAL,
              variable=self.sample_tolerance, label="Tolerance",
              command=lambda _: self.update_preview_if_samples()).pack(fill=tk.X, padx=5, pady=2)

        # Color samples display
        samples_frame = ttk.LabelFrame(self.control_frame, text="Color Samples")
        samples_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create a frame for the samples list
        self.samples_list_frame = ttk.Frame(samples_frame)
        self.samples_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a canvas to display color swatches
        self.samples_canvas = tk.Canvas(self.samples_list_frame, height=120, bg="white")
        self.samples_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add a scrollbar to the canvas
        samples_scrollbar = ttk.Scrollbar(self.samples_list_frame, orient=tk.VERTICAL,
                                         command=self.samples_canvas.yview)
        samples_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.samples_canvas.configure(yscrollcommand=samples_scrollbar.set)

        # Create a frame inside the canvas to hold the color samples
        self.samples_inner_frame = ttk.Frame(self.samples_canvas)
        self.samples_window = self.samples_canvas.create_window((0, 0), window=self.samples_inner_frame, anchor="nw")

        # Configure the canvas to resize the window when the frame changes size
        def configure_samples_frame(event):
            self.samples_canvas.configure(scrollregion=self.samples_canvas.bbox("all"))
            self.samples_canvas.itemconfig(self.samples_window, width=event.width)

        self.samples_inner_frame.bind("<Configure>", configure_samples_frame)

        # Create a listbox to track samples (hidden, just for selection management)
        self.samples_listbox = tk.Listbox(self.root)

        # Add buttons for managing samples
        samples_buttons_frame = ttk.Frame(samples_frame)
        samples_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(samples_buttons_frame, text="Remove Selected",
                  command=self.remove_selected_sample).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
        ttk.Button(samples_buttons_frame, text="Clear All",
                  command=self.clear_all_samples).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=2)

        # HSV controls
        hsv_frame = ttk.LabelFrame(self.control_frame, text="HSV Color Range")
        hsv_frame.pack(fill=tk.X, padx=5, pady=5)

        # Hue range (0-179 in OpenCV)
        ttk.Label(hsv_frame, text="Hue Range:").pack(anchor=tk.W, padx=5, pady=2)
        h_frame = ttk.Frame(hsv_frame)
        h_frame.pack(fill=tk.X, padx=5, pady=2)

        Scale(h_frame, from_=0, to=179, orient=tk.HORIZONTAL, variable=self.h_min,
              label="Min", command=lambda _: self.update_preview()).pack(side=tk.LEFT, fill=tk.X, expand=True)
        Scale(h_frame, from_=0, to=179, orient=tk.HORIZONTAL, variable=self.h_max,
              label="Max", command=lambda _: self.update_preview()).pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # Saturation range (0-255)
        ttk.Label(hsv_frame, text="Saturation Range:").pack(anchor=tk.W, padx=5, pady=2)
        s_frame = ttk.Frame(hsv_frame)
        s_frame.pack(fill=tk.X, padx=5, pady=2)

        Scale(s_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=self.s_min,
              label="Min", command=lambda _: self.update_preview()).pack(side=tk.LEFT, fill=tk.X, expand=True)
        Scale(s_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=self.s_max,
              label="Max", command=lambda _: self.update_preview()).pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # Value range (0-255)
        ttk.Label(hsv_frame, text="Value Range:").pack(anchor=tk.W, padx=5, pady=2)
        v_frame = ttk.Frame(hsv_frame)
        v_frame.pack(fill=tk.X, padx=5, pady=2)

        Scale(v_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=self.v_min,
              label="Min", command=lambda _: self.update_preview()).pack(side=tk.LEFT, fill=tk.X, expand=True)
        Scale(v_frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=self.v_max,
              label="Max", command=lambda _: self.update_preview()).pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # Preset buttons for common colors
        preset_frame = ttk.LabelFrame(self.control_frame, text="Color Presets")
        preset_frame.pack(fill=tk.X, padx=5, pady=5)

        presets_grid = ttk.Frame(preset_frame)
        presets_grid.pack(fill=tk.X, padx=5, pady=5)

        # Green presets
        ttk.Button(presets_grid, text="Light Green",
                   command=lambda: self.set_preset(40, 80, 40, 255, 40, 255)).grid(row=0, column=0, padx=2, pady=2)
        ttk.Button(presets_grid, text="Dark Green",
                   command=lambda: self.set_preset(40, 80, 100, 255, 10, 150)).grid(row=0, column=1, padx=2, pady=2)
        ttk.Button(presets_grid, text="Forest Green",
                   command=lambda: self.set_preset(35, 50, 100, 255, 20, 200)).grid(row=1, column=0, padx=2, pady=2)
        ttk.Button(presets_grid, text="Olive Green",
                   command=lambda: self.set_preset(25, 40, 50, 200, 20, 150)).grid(row=1, column=1, padx=2, pady=2)

        # Polygon controls
        polygon_frame = ttk.LabelFrame(self.control_frame, text="Polygon Settings")
        polygon_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(polygon_frame, text="Simplification Factor:").pack(anchor=tk.W, padx=5, pady=2)
        Scale(polygon_frame, from_=0.0001, to=0.01, resolution=0.0001, orient=tk.HORIZONTAL,
              variable=self.epsilon_factor, label="Epsilon",
              command=lambda _: self.update_preview()).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(polygon_frame, text="Minimum Area (pixels²):").pack(anchor=tk.W, padx=5, pady=2)
        Scale(polygon_frame, from_=10, to=1000, orient=tk.HORIZONTAL,
              variable=self.min_area, label="Min Area",
              command=lambda _: self.update_preview()).pack(fill=tk.X, padx=5, pady=2)

        # Action buttons
        action_frame = ttk.LabelFrame(self.control_frame, text="Actions")
        action_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(action_frame, text="Update Preview", command=self.update_preview).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(action_frame, text="Export to Shapefile", command=self.export_to_shapefile).pack(fill=tk.X, padx=5, pady=5)

    def set_preset(self, h_min, h_max, s_min, s_max, v_min, v_max):
        self.h_min.set(h_min)
        self.h_max.set(h_max)
        self.s_min.set(s_min)
        self.s_max.set(s_max)
        self.v_min.set(v_min)
        self.v_max.set(v_max)
        self.update_preview()

    def load_image(self):
        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.tif;*.tiff"), ("All files", "*.*")]
        )
        if file_path:
            self.image_path = file_path
            self.load_image_from_path(file_path)

    def load_image_from_path(self, path):
        try:
            # Load with PIL for display
            self.original_image = Image.open(path)

            # Load with OpenCV for processing
            self.cv_image = cv2.imread(path)
            self.cv_image = cv2.cvtColor(self.cv_image, cv2.COLOR_BGR2RGB)

            # Update display
            self.update_image_display()
            self.status_var.set(f"Loaded image: {path}")

            # Update preview if we have all necessary data
            if self.cv_image is not None:
                self.update_preview()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.status_var.set(f"Error loading image: {str(e)}")

    def load_config(self):
        file_path = filedialog.askopenfilename(
            title="Select Georeferencing Config File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.config_path = file_path
            self.load_config_from_path(file_path)

    def load_config_from_path(self, path):
        try:
            with open(path, 'r') as f:
                self.georef_data = json.load(f)

            # Check if the config has the required fields
            if not all(key in self.georef_data for key in ["image_path", "image_width", "image_height", "points"]):
                raise ValueError("Invalid georeferencing config file format")

            # If the image hasn't been loaded yet, try to load it from the config
            if self.original_image is None and os.path.exists(self.georef_data["image_path"]):
                self.image_path = self.georef_data["image_path"]
                self.load_image_from_path(self.image_path)

            self.status_var.set(f"Loaded georeferencing config: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load config: {str(e)}")
            self.status_var.set(f"Error loading config: {str(e)}")

    def update_image_display(self):
        if self.original_image:
            # Display original image
            self.display_image(self.original_image, self.original_canvas)

    def display_image(self, image, canvas):
        # Resize image based on scale factor
        width = int(image.width * self.scale_factor)
        height = int(image.height * self.scale_factor)
        resized_image = image.resize((width, height), Image.LANCZOS)

        # Convert to PhotoImage
        tk_image = ImageTk.PhotoImage(resized_image)

        # Store reference to prevent garbage collection
        canvas.image = tk_image

        # Update canvas
        canvas.delete("all")
        canvas.create_image(0, 0, anchor=tk.NW, image=tk_image)
        canvas.config(scrollregion=canvas.bbox(tk.ALL))

    def start_color_sampling(self):
        """Start color sampling mode."""
        if self.cv_image is None:
            messagebox.showwarning("Warning", "Please load an image first.")
            return

        self.sampling_mode = True
        self.status_var.set("Click on the image to sample colors. Press ESC to exit sampling mode.")

        # Change cursor to indicate sampling mode
        self.original_canvas.config(cursor="crosshair")

        # Show the color preview box (it will be positioned in on_canvas_move)
        self.color_preview.place(x=0, y=0)

        # Bind Escape key to exit sampling mode
        self.root.bind("<Escape>", self.stop_color_sampling)

    def stop_color_sampling(self, event=None):
        """Stop color sampling mode."""
        self.sampling_mode = False
        self.status_var.set("Color sampling mode ended.")

        # Reset cursor
        self.original_canvas.config(cursor="")

        # Hide the color preview box
        self.color_preview.place_forget()

        # Unbind Escape key
        self.root.unbind("<Escape>")

    def on_canvas_click(self, event):
        """Handle canvas click events for color sampling."""
        if not self.sampling_mode or self.cv_image is None:
            return

        # Get canvas coordinates
        canvas_x = self.original_canvas.canvasx(event.x)
        canvas_y = self.original_canvas.canvasy(event.y)

        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)

        # Check if coordinates are within image bounds
        if 0 <= original_x < self.cv_image.shape[1] and 0 <= original_y < self.cv_image.shape[0]:
            # Get RGB color at the clicked point
            rgb_color = self.cv_image[original_y, original_x]

            # Convert to HSV
            hsv_color = cv2.cvtColor(np.uint8([[rgb_color]]), cv2.COLOR_RGB2HSV)[0][0]

            # Add to samples
            self.add_color_sample(hsv_color)

            # Update preview
            self.update_preview_with_samples()

    def on_mousewheel(self, event):
        """Handle mousewheel events for scrolling the control panel."""
        # Check if mouse is over the control canvas
        x, y = event.x_root, event.y_root
        canvas_x = self.control_canvas.winfo_rootx()
        canvas_y = self.control_canvas.winfo_rooty()
        canvas_width = self.control_canvas.winfo_width()
        canvas_height = self.control_canvas.winfo_height()

        # Only scroll if mouse is over the control canvas
        if (canvas_x <= x <= canvas_x + canvas_width and
            canvas_y <= y <= canvas_y + canvas_height):
            self.control_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def on_canvas_move(self, event):
        """Handle mouse movement over the canvas to show color info."""
        if self.cv_image is None:
            return

        # Get canvas coordinates
        canvas_x = self.original_canvas.canvasx(event.x)
        canvas_y = self.original_canvas.canvasy(event.y)

        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)

        # Check if coordinates are within image bounds
        if 0 <= original_x < self.cv_image.shape[1] and 0 <= original_y < self.cv_image.shape[0]:
            # Get RGB color at the mouse position
            rgb_color = self.cv_image[original_y, original_x]

            # Convert to HSV
            hsv_color = cv2.cvtColor(np.uint8([[rgb_color]]), cv2.COLOR_RGB2HSV)[0][0]

            # Update color info label
            self.color_info_var.set(f"Position: ({original_x}, {original_y}) | RGB: {tuple(rgb_color)} | HSV: {tuple(hsv_color)}")

            # If in sampling mode, update the color preview box
            if self.sampling_mode:
                # Get the mouse position relative to the screen
                x = self.root.winfo_pointerx()
                y = self.root.winfo_pointery()

                # Convert RGB to hex color
                hex_color = f'#{rgb_color[0]:02x}{rgb_color[1]:02x}{rgb_color[2]:02x}'

                # Update color preview box
                self.color_preview.configure(bg=hex_color)

                # Position the color preview box near the cursor
                self.color_preview.place(x=x+20, y=y+20)

    def create_color_swatch(self, hsv_color, size=15):
        """Create a color swatch image for the given HSV color."""
        # Convert HSV to RGB
        h, s, v = hsv_color
        hsv_array = np.uint8([[[h, s, v]]])
        rgb_array = cv2.cvtColor(hsv_array, cv2.COLOR_HSV2RGB)[0][0]

        # Create a small image with the color
        img = Image.new('RGB', (size, size), tuple(rgb_array))
        return ImageTk.PhotoImage(img)

    def add_color_sample(self, hsv_color):
        """Add a color sample to the list."""
        h, s, v = hsv_color

        # Add to samples list
        self.color_samples.append((h, s, v))

        # Get sample index
        sample_index = len(self.color_samples)

        # Create a color swatch
        swatch = self.create_color_swatch(hsv_color, size=25)

        # Store the swatch to prevent garbage collection
        if not hasattr(self, 'color_swatches'):
            self.color_swatches = []
        self.color_swatches.append(swatch)

        # Convert HSV to RGB for display
        hsv_array = np.uint8([[[h, s, v]]])
        rgb_array = cv2.cvtColor(hsv_array, cv2.COLOR_HSV2RGB)[0][0]
        hex_color = f'#{rgb_array[0]:02x}{rgb_array[1]:02x}{rgb_array[2]:02x}'

        # Create a frame for this sample in the samples canvas
        sample_frame = ttk.Frame(self.samples_inner_frame)
        sample_frame.pack(fill=tk.X, padx=5, pady=2)

        # Create a colored rectangle for the swatch
        swatch_label = tk.Label(sample_frame, bg=hex_color, width=3, height=1)
        swatch_label.pack(side=tk.LEFT, padx=5, pady=2)

        # Create a label for the text
        text = f"Sample {sample_index}: H={h}, S={s}, V={v}"
        text_label = ttk.Label(sample_frame, text=text)
        text_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Add to hidden listbox for selection management
        self.samples_listbox.insert(tk.END, text)

        # Update status
        self.status_var.set(f"Added color sample: H={h}, S={s}, V={v}")

        # Update the samples canvas scrollregion
        self.samples_canvas.configure(scrollregion=self.samples_canvas.bbox("all"))

    def remove_selected_sample(self):
        """Remove the selected color sample."""
        selected_indices = self.samples_listbox.curselection()
        if not selected_indices:
            return

        # Remove from end to beginning to avoid index shifting
        for i in sorted(selected_indices, reverse=True):
            del self.color_samples[i]
            if hasattr(self, 'color_swatches') and i < len(self.color_swatches):
                del self.color_swatches[i]
            self.samples_listbox.delete(i)

        # Clear and rebuild the samples display
        for widget in self.samples_inner_frame.winfo_children():
            widget.destroy()

        # Rebuild the samples display
        for i, (h, s, v) in enumerate(self.color_samples):
            # Convert HSV to RGB for display
            hsv_array = np.uint8([[[h, s, v]]])
            rgb_array = cv2.cvtColor(hsv_array, cv2.COLOR_HSV2RGB)[0][0]
            hex_color = f'#{rgb_array[0]:02x}{rgb_array[1]:02x}{rgb_array[2]:02x}'

            # Create a frame for this sample
            sample_frame = ttk.Frame(self.samples_inner_frame)
            sample_frame.pack(fill=tk.X, padx=5, pady=2)

            # Create a colored rectangle for the swatch
            swatch_label = tk.Label(sample_frame, bg=hex_color, width=3, height=1)
            swatch_label.pack(side=tk.LEFT, padx=5, pady=2)

            # Create a label for the text
            text = f"Sample {i+1}: H={h}, S={s}, V={v}"
            text_label = ttk.Label(sample_frame, text=text)
            text_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Update the samples canvas scrollregion
        self.samples_canvas.configure(scrollregion=self.samples_canvas.bbox("all"))

        # Update preview
        self.update_preview_with_samples()

    def clear_all_samples(self):
        """Clear all color samples."""
        if not self.color_samples:
            return

        if messagebox.askyesno("Confirm", "Are you sure you want to clear all color samples?"):
            self.color_samples = []
            if hasattr(self, 'color_swatches'):
                self.color_swatches = []

            # Clear the listbox
            self.samples_listbox.delete(0, tk.END)

            # Clear the samples display
            for widget in self.samples_inner_frame.winfo_children():
                widget.destroy()

            # Update the samples canvas scrollregion
            self.samples_canvas.configure(scrollregion=self.samples_canvas.bbox("all"))

            # Update preview
            self.update_preview()

    def update_preview_if_samples(self, _=None):
        """Update preview if there are color samples."""
        if self.color_samples:
            self.update_preview_with_samples()

    def update_preview_with_samples(self):
        """Update preview using the color samples."""
        if not self.color_samples or self.cv_image is None:
            return

        # Convert to HSV
        hsv_image = cv2.cvtColor(self.cv_image, cv2.COLOR_RGB2HSV)

        # Create a combined mask from all samples
        tolerance = self.sample_tolerance.get()
        combined_mask = np.zeros((self.cv_image.shape[0], self.cv_image.shape[1]), dtype=np.uint8)

        for h, s, v in self.color_samples:
            # Create range with tolerance
            lower_bound = np.array([max(0, h - tolerance), max(0, s - tolerance), max(0, v - tolerance)])
            upper_bound = np.array([min(179, h + tolerance), min(255, s + tolerance), min(255, v + tolerance)])

            # Create mask for this sample
            sample_mask = cv2.inRange(hsv_image, lower_bound, upper_bound)

            # Combine with the overall mask
            combined_mask = cv2.bitwise_or(combined_mask, sample_mask)

        self.mask = combined_mask

        # Apply morphological operations to clean up the mask
        kernel = np.ones((5, 5), np.uint8)
        self.mask = cv2.morphologyEx(self.mask, cv2.MORPH_OPEN, kernel)
        self.mask = cv2.morphologyEx(self.mask, cv2.MORPH_CLOSE, kernel)

        # Process the mask to find contours and update the display
        self.process_mask_and_update_display()

    def update_preview(self):
        """Update preview using the HSV sliders."""
        if self.cv_image is None:
            messagebox.showwarning("Warning", "Please load an image first.")
            return

        # Convert to HSV
        hsv_image = cv2.cvtColor(self.cv_image, cv2.COLOR_RGB2HSV)

        # Create mask for the specified HSV range
        lower_bound = np.array([self.h_min.get(), self.s_min.get(), self.v_min.get()])
        upper_bound = np.array([self.h_max.get(), self.s_max.get(), self.v_max.get()])
        self.mask = cv2.inRange(hsv_image, lower_bound, upper_bound)

        # Apply morphological operations to clean up the mask
        kernel = np.ones((5, 5), np.uint8)
        self.mask = cv2.morphologyEx(self.mask, cv2.MORPH_OPEN, kernel)
        self.mask = cv2.morphologyEx(self.mask, cv2.MORPH_CLOSE, kernel)

        # Process the mask to find contours and update the display
        self.process_mask_and_update_display()

    def process_mask_and_update_display(self):
        """Process the mask to find contours and update the display."""
        if self.mask is None:
            return

        # Find contours
        contours, _ = cv2.findContours(self.mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Filter contours by area and simplify
        self.contours = []
        self.polygons = []

        for contour in contours:
            area = cv2.contourArea(contour)
            if area >= self.min_area.get():
                # Simplify contour
                epsilon = self.epsilon_factor.get() * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                self.contours.append(approx)

                # Convert to polygon
                points = []
                for point in approx:
                    x, y = point[0]
                    points.append((x, y))

                if len(points) >= 3:  # Need at least 3 points for a polygon
                    self.polygons.append(Polygon(points))

        # Display mask
        mask_rgb = cv2.cvtColor(self.mask, cv2.COLOR_GRAY2RGB)
        mask_pil = Image.fromarray(mask_rgb)
        self.display_image(mask_pil, self.mask_canvas)

        # Display contours
        contour_image = self.cv_image.copy()
        cv2.drawContours(contour_image, self.contours, -1, (255, 0, 0), 2)
        contour_pil = Image.fromarray(contour_image)
        self.display_image(contour_pil, self.contour_canvas)

        # Update status
        self.status_var.set(f"Found {len(self.contours)} contours. Adjust settings as needed.")

    def pixel_to_geo(self, x, y):
        """Convert pixel coordinates to geographic coordinates using the georeferencing data."""
        if not self.georef_data or not self.georef_data["points"]:
            return None, None

        # For simplicity, we'll use a simple linear transformation based on the first point
        # In a real application, you would use a more sophisticated transformation
        ref_point = self.georef_data["points"][0]
        ref_px, ref_py = ref_point["pixel_x"], ref_point["pixel_y"]
        ref_lat, ref_lon = ref_point["latitude"], ref_point["longitude"]

        # Calculate scale factors (this is a very simple approximation)
        # In reality, you would use a proper georeferencing transformation
        lat_scale = 0.0001  # Arbitrary scale factor
        lon_scale = 0.0001  # Arbitrary scale factor

        # Calculate new coordinates
        lat = ref_lat - (y - ref_py) * lat_scale  # Subtract because y increases downward
        lon = ref_lon + (x - ref_px) * lon_scale

        return lat, lon

    def export_to_shapefile(self):
        if not self.polygons:
            messagebox.showwarning("Warning", "No polygons to export.")
            return

        if not self.georef_data:
            messagebox.showwarning("Warning", "No georeferencing data loaded. The shapefile will not be georeferenced.")
            return

        try:
            # Create GeoDataFrame
            geo_polygons = []
            data = {"id": [], "area_px": []}

            for i, polygon in enumerate(self.polygons):
                # Get pixel coordinates
                pixel_coords = list(polygon.exterior.coords)

                # Convert to geographic coordinates
                geo_coords = []
                for px, py in pixel_coords:
                    lat, lon = self.pixel_to_geo(px, py)
                    if lat is not None and lon is not None:
                        geo_coords.append((lon, lat))  # GeoDataFrame expects (lon, lat) order

                if len(geo_coords) >= 3:  # Need at least 3 points for a polygon
                    geo_poly = Polygon(geo_coords)
                    geo_polygons.append(geo_poly)
                    data["id"].append(i + 1)
                    data["area_px"].append(polygon.area)

            if not geo_polygons:
                messagebox.showwarning("Warning", "Failed to create valid geographic polygons.")
                return

            gdf = gpd.GeoDataFrame(data, geometry=geo_polygons, crs="EPSG:4326")

            # Save to file
            file_path = filedialog.asksaveasfilename(
                title="Save Shapefile",
                defaultextension=".shp",
                filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
                initialfile="green_areas.shp"
            )

            if file_path:
                gdf.to_file(file_path)
                self.status_var.set(f"Exported polygons to {file_path}")
                messagebox.showinfo("Success", f"Polygons exported to {file_path}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export shapefile: {str(e)}")
            self.status_var.set(f"Error exporting shapefile: {str(e)}")

def main():
    root = tk.Tk()
    app = ColorExtractorApp(root)

    # Try to load default files if they exist
    default_image = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\HCV_1.png"
    default_config = "map_georef_config.json"

    if os.path.exists(default_image):
        app.load_image_from_path(default_image)

    if os.path.exists(default_config):
        app.load_config_from_path(default_config)

    root.mainloop()

if __name__ == "__main__":
    main()
