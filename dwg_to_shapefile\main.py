import subprocess
import os

# Path ke file DWG dan output shapefile
dwg_path = r"C:\Users\<USER>\Downloads\drive-download-20250527T030512Z-1-001\HGU 8 UTM.DWG"
output_shapefile = r"D:\Gawean Rebinmas\Geo Processing GIS Alat\dwg_to_shapefile\HGU_8_UTM_Polygon.shp"

# Pastikan ogr2ogr tersedia di sistem (biasanya diinstal bersama GDAL atau QGIS)
ogr2ogr_path = "ogr2ogr"  # Jika tidak ada di PATH, masukkan path lengkap ke ogr2ogr.exe

# Perintah untuk mengonversi DWG ke shapefile
command = [
    ogr2ogr_path,
    "-f", "ESRI Shapefile",
    output_shapefile,
    dwg_path,
    "-where", "OGR_GEOMETRY='POLYGON'",  # Hanya ambil geometri polygon
    "-overwrite"
]

# Jalankan perintah
try:
    subprocess.run(command, check=True)
    print(f"Shapefile berhasil disimpan ke: {output_shapefile}")
except subprocess.CalledProcessError as e:
    print("Gagal mengonversi DWG ke shapefile. Pastikan GDAL mendukung DWG atau coba konversi manual ke DXF.")
    print(e)
except FileNotFoundError:
    print("Perintah 'ogr2ogr' tidak ditemukan. Pastikan GDAL terinstal dan ditambahkan ke PATH.")