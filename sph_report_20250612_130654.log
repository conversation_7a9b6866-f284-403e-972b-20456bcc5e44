2025-06-12 13:07:25,491 - INFO - Reading SPH data from: D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/shapefile/PGe/PGE_2025_with_inclave_terupdate_tree.shp
2025-06-12 13:07:25,966 - INFO - Successfully read 304 records
2025-06-12 13:07:25,968 - INFO - Columns: ['Id', 'luas_1', 'DIVISI', 'SUB_DIVISI', 'BLOK', 'LUAS_AUTO', '<PERSON><PERSON><PERSON>_<PERSON><PERSON>', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained_', 'total_incl', 'luas_netto']
2025-06-12 13:07:25,971 - INFO - Generating summary report...
2025-06-12 13:07:25,972 - INFO - Column mapping applied. Available columns: ['Id', 'luas_1', 'Divisi', 'SUB_DIVISI', 'BL<PERSON>', 'LUAS_AUTO', '<PERSON><PERSON><PERSON>_<PERSON><PERSON>', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained', 'total_incl', 'luas_netto']
2025-06-12 13:07:25,976 - INFO - Filtering for Boundary records with HCV_Catego = 0
2025-06-12 13:07:25,978 - INFO - Filtered 114 Boundary records with HCV_Catego = 0 from 304 total records
2025-06-12 13:07:25,979 - INFO - Calculating inclave areas...
2025-06-12 13:07:25,980 - INFO - Found 190 inclave records
2025-06-12 13:07:26,016 - INFO - Calculated inclave areas for 111 boundary groups
2025-06-12 13:07:26,029 - INFO - Generated summary report with 111 records
2025-06-12 13:07:26,031 - INFO - Exporting report to: SPH_Report_Summary_20250612_130726.xlsx
2025-06-12 13:07:26,263 - INFO - Column mapping applied. Available columns: ['Id', 'luas_1', 'Divisi', 'SUB_DIVISI', 'BLOK', 'LUAS_AUTO', 'Jumlah_Poh', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained', 'total_incl', 'luas_netto']
2025-06-12 13:07:26,263 - INFO - Filtering for Boundary records with HCV_Catego = 0
2025-06-12 13:07:26,264 - INFO - Filtered 114 Boundary records with HCV_Catego = 0 from 304 total records
2025-06-12 13:07:26,362 - INFO - Report successfully exported to: SPH_Report_Summary_20250612_130726.xlsx
