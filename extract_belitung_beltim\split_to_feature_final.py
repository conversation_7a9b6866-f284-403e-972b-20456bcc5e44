import geopandas as gpd
import os

print("=== Pemrosesan Area Tanam Belitung dan Belitung Timur (FINAL) ===")

# Path file
area_tanam_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/Area_Tanam_Outer_Layer_backup.shp"
batas_desa_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/batas_desa_belitung/batas_desa_belitung.shp"

# Periksa keberadaan file
if not os.path.exists(area_tanam_path):
    print(f"ERROR: File area tanam tidak ditemukan: {area_tanam_path}")
    exit(1)
    
if not os.path.exists(batas_desa_path):
    print(f"ERROR: File batas desa tidak ditemukan: {batas_desa_path}")
    exit(1)

print("✓ File input ditemukan")

# Muat data
print("\n1. Memuat data shapefile...")
area_tanam = gpd.read_file(area_tanam_path)
batas_desa = gpd.read_file(batas_desa_path)

print(f"   - Area tanam: {len(area_tanam)} fitur")
print(f"   - Batas desa: {len(batas_desa)} fitur")

# Periksa dan perbaiki CRS
print("\n2. Menganalisis dan memperbaiki sistem koordinat...")
print(f"   - Area tanam CRS: {area_tanam.crs}")
print(f"   - Batas desa CRS: {batas_desa.crs}")

# Periksa bounds untuk menentukan sistem koordinat yang tepat
at_bounds = area_tanam.total_bounds
bd_bounds = batas_desa.total_bounds

print(f"\n   Bounds analysis:")
print(f"   - Area tanam: {at_bounds}")
print(f"   - Batas desa: {bd_bounds}")

# Area tanam tampaknya sudah dalam WGS84 (koordinat geografis)
if area_tanam.crs is None:
    area_tanam = area_tanam.set_crs('EPSG:4326')
    print("   ✓ Area tanam CRS diatur ke WGS84")

# Batas desa memiliki koordinat UTM (nilai besar dalam meter)
# Untuk wilayah Belitung di Indonesia, kemungkinan menggunakan UTM Zone 48S (EPSG:32748)
if batas_desa.crs is None:
    # Deteksi zona UTM berdasarkan koordinat
    if 700000 <= bd_bounds[0] <= 1000000 and 9600000 <= bd_bounds[1] <= 9800000:
        print("   ✓ Mendeteksi batas desa menggunakan UTM Zone 48S")
        batas_desa = batas_desa.set_crs('EPSG:32748')  # UTM Zone 48S
        print("   ✓ Batas desa CRS diatur ke UTM Zone 48S")
    else:
        print("   ⚠️  Tidak dapat mendeteksi CRS batas desa secara otomatis")
        print("   💡 Mencoba UTM Zone 48S sebagai default untuk wilayah Belitung")
        batas_desa = batas_desa.set_crs('EPSG:32748')

# Konversi batas desa ke WGS84 untuk menyamakan dengan area tanam
print("   🔄 Mengkonversi batas desa ke WGS84...")
batas_desa_wgs84 = batas_desa.to_crs('EPSG:4326')

print(f"   - Final CRS Area tanam: {area_tanam.crs}")
print(f"   - Final CRS Batas desa: {batas_desa_wgs84.crs}")

# Periksa bounds setelah konversi
bd_wgs84_bounds = batas_desa_wgs84.total_bounds
print(f"\n3. Bounds setelah konversi:")
print(f"   - Area tanam WGS84: Lon {at_bounds[0]:.6f} to {at_bounds[2]:.6f}, Lat {at_bounds[1]:.6f} to {at_bounds[3]:.6f}")
print(f"   - Batas desa WGS84: Lon {bd_wgs84_bounds[0]:.6f} to {bd_wgs84_bounds[2]:.6f}, Lat {bd_wgs84_bounds[1]:.6f} to {bd_wgs84_bounds[3]:.6f}")

# Periksa overlap
x_overlap = (at_bounds[0] <= bd_wgs84_bounds[2]) and (at_bounds[2] >= bd_wgs84_bounds[0])
y_overlap = (at_bounds[1] <= bd_wgs84_bounds[3]) and (at_bounds[3] >= bd_wgs84_bounds[1])
print(f"   - Potensi overlap: {x_overlap and y_overlap}")

# Validasi geometri
print(f"\n3.5. Validasi geometri...")
print(f"   - Tipe geometri area tanam: {area_tanam.geometry.geom_type.unique()}")
print(f"   - Tipe geometri batas desa: {batas_desa_wgs84.geometry.geom_type.unique()}")

# Perbaiki geometri yang tidak valid
area_tanam_clean = area_tanam.copy()
batas_desa_clean = batas_desa_wgs84.copy()

if not area_tanam_clean.is_valid.all():
    print("   🔧 Memperbaiki geometri area tanam yang tidak valid...")
    area_tanam_clean['geometry'] = area_tanam_clean.geometry.buffer(0)

if not batas_desa_clean.is_valid.all():
    print("   🔧 Memperbaiki geometri batas desa yang tidak valid...")
    batas_desa_clean['geometry'] = batas_desa_clean.geometry.buffer(0)

# Periksa kolom WADMKK
print(f"\n4. Memeriksa kolom WADMKK...")
if 'WADMKK' in batas_desa_clean.columns:
    wadmkk_values = batas_desa_clean['WADMKK'].unique()
    print(f"   - Ditemukan {len(wadmkk_values)} nilai WADMKK unik:")
    for wadmkk in sorted(wadmkk_values):
        count = len(batas_desa_clean[batas_desa_clean['WADMKK'] == wadmkk])
        print(f"     • {wadmkk}: {count} fitur")
else:
    print("   ❌ Kolom WADMKK tidak ditemukan!")
    print("   📋 Kolom yang tersedia:", list(batas_desa_clean.columns))

# Lakukan interseksi
print("\n5. Melakukan interseksi dengan CRS yang sudah diperbaiki...")
try:
    # Tambahkan parameter keep_geom_type=False untuk menangani GeometryCollection
    intersected = gpd.overlay(area_tanam_clean, batas_desa_clean, how='intersection', keep_geom_type=False)
    print(f"   - Hasil interseksi: {len(intersected)} fitur")
    
    if len(intersected) > 0:
        print("   ✅ INTERSEKSI BERHASIL!")
        
        # Filter hanya geometri Polygon dan MultiPolygon
        valid_geom_types = ['Polygon', 'MultiPolygon']
        intersected_filtered = intersected[intersected.geometry.geom_type.isin(valid_geom_types)]
        print(f"   - Setelah filter geometri: {len(intersected_filtered)} fitur")
        
        # Tampilkan distribusi WADMKK setelah interseksi
        if 'WADMKK' in intersected_filtered.columns:
            print(f"\n6. Distribusi WADMKK setelah interseksi:")
            wadmkk_intersect = intersected_filtered['WADMKK'].value_counts()
            for wadmkk, count in wadmkk_intersect.items():
                print(f"     • {wadmkk}: {count} fitur")
                
            # Dissolve berdasarkan WADMKK
            print("\n7. Menggabungkan berdasarkan WADMKK...")
            dissolved = intersected_filtered.dissolve(by='WADMKK')
            print(f"   - Hasil akhir: {len(dissolved)} wilayah")
            
            # Tampilkan ringkasan dengan perhitungan luas yang akurat
            print("\n8. Ringkasan hasil akhir:")
            total_area = 0
            for idx, row in dissolved.iterrows():
                try:
                    # Konversi ke UTM untuk perhitungan luas yang akurat
                    temp_geom = gpd.GeoDataFrame([row], crs=dissolved.crs).to_crs('EPSG:32748')
                    area_ha = temp_geom.geometry.area.iloc[0] / 10000
                    total_area += area_ha
                    print(f"     • {idx}: {area_ha:,.2f} hektar")
                except Exception as e:
                    print(f"     • {idx}: (error menghitung luas: {e})")
                    
            print(f"   📊 Total luas area tanam: {total_area:,.2f} hektar")
            
            # Simpan hasil
            output_file = "Area_Tanam_Belitung_BelitungTimur_Success.shp"
            print(f"\n9. Menyimpan hasil ke: {output_file}")
            dissolved.to_file(output_file)
            
            print("\n" + "="*60)
            print("🎉 PEMROSESAN BERHASIL! 🎉")
            print("="*60)
            print(f"✅ File output: {output_file}")
            print(f"✅ Total wilayah: {len(dissolved)}")
            print(f"✅ Total luas: {total_area:,.2f} hektar")
            print("\n📍 Wilayah yang berhasil dipisah:")
            for i, idx in enumerate(dissolved.index, 1):
                print(f"   {i}. {idx}")
                
            print("\n📝 File ini berisi area tanam yang sudah dibagi berdasarkan:")
            print("   - Belitung: Area tanam di wilayah Kabupaten Belitung")  
            print("   - Belitung Timur: Area tanam di wilayah Kabupaten Belitung Timur")
                
        else:
            print("   ❌ Kolom WADMKK tidak ada dalam hasil interseksi")
            
    else:
        print("   ❌ Masih tidak ada interseksi setelah perbaikan CRS")
        print("   💡 Kemungkinan area tanam berada di luar wilayah batas desa")
        print("   🔍 Coba periksa apakah area tanam benar-benar berada di wilayah Belitung")
        
except Exception as e:
    print(f"   ❌ Error saat interseksi: {e}")
    import traceback
    print("   Debug info:", traceback.format_exc())

print("\n=== PEMROSESAN SELESAI ===") 