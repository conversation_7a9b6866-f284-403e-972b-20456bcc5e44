#!/usr/bin/env python3
"""
Simple launcher for SPH Report Generator
Provides easy access with default path configuration
"""

import os
import sys
from pathlib import Path
from sph_report_generator import SPHReportGenerator

def main():
    """Main launcher function"""
    print("=" * 60)
    print("     SPH REPORT GENERATOR - QUICK LAUNCHER")
    print("=" * 60)
    print()
    
    # Default path as specified by user
    default_sph_path = r"D:\Gawean Rebinmas\Geo  Processing GIS Alat\report_SPH_unplanted"
    
    print(f"Default SPH Path: {default_sph_path}")
    print()
    
    # Check if default path exists
    if os.path.exists(default_sph_path):
        print("✓ Default path found")
        print(f"Files in directory: {len(os.listdir(default_sph_path))}")
    else:
        print("⚠ Default path not found - you'll need to browse for SPH files")
    
    print()
    print("Choose an option:")
    print("1. GUI Mode (Recommended)")
    print("2. CLI Mode") 
    print("3. Test with Sample Data")
    print("4. Exit")
    print()
    
    try:
        choice = input("Enter your choice (1-4): ").strip()
        
        generator = SPHReportGenerator()
        
        if choice == "1":
            print("\nStarting GUI Mode...")
            generator.run_gui_mode()
            
        elif choice == "2":
            print("\nStarting CLI Mode...")
            generator.run_cli_mode()
            
        elif choice == "3":
            print("\nRunning test with sample data...")
            from test_sph_report import test_sph_report_generator
            test_sph_report_generator()
            
        elif choice == "4":
            print("Goodbye!")
            
        else:
            print("Invalid choice. Please run the program again.")
            
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {str(e)}")
        print("Please check the log files for more details.")

if __name__ == "__main__":
    main() 