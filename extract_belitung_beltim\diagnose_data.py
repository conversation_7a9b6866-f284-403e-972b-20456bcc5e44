import geopandas as gpd
import matplotlib.pyplot as plt

print("=== DIAGNOSA DATA SHAPEFILE ===")

# Path file
area_tanam_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/Area_Tanam_Outer_Layer_backup.shp"
batas_desa_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/batas_desa_belitung/batas_desa_belitung.shp"

# Muat data
print("1. Memuat data...")
area_tanam = gpd.read_file(area_tanam_path)
batas_desa = gpd.read_file(batas_desa_path)

print(f"   - Area tanam: {len(area_tanam)} fitur")
print(f"   - Batas desa: {len(batas_desa)} fitur")

# Periksa CRS (Coordinate Reference System)
print("\n2. Sistem Koordinat (CRS):")
print(f"   - Area tanam CRS: {area_tanam.crs}")
print(f"   - Batas desa CRS: {batas_desa.crs}")

if area_tanam.crs != batas_desa.crs:
    print("   ⚠️  PERINGATAN: CRS berbeda!")
    print("   Akan menyamakan CRS ke WGS84...")
    area_tanam = area_tanam.to_crs('EPSG:4326')
    batas_desa = batas_desa.to_crs('EPSG:4326')
    print("   ✓ CRS sudah disamakan ke WGS84")

# Periksa bounds (batas area)
print("\n3. Batas Area (Bounds):")
at_bounds = area_tanam.total_bounds
bd_bounds = batas_desa.total_bounds

print(f"   Area tanam bounds:")
print(f"     - MinX: {at_bounds[0]:.6f}, MinY: {at_bounds[1]:.6f}")
print(f"     - MaxX: {at_bounds[2]:.6f}, MaxY: {at_bounds[3]:.6f}")

print(f"   Batas desa bounds:")
print(f"     - MinX: {bd_bounds[0]:.6f}, MinY: {bd_bounds[1]:.6f}")
print(f"     - MaxX: {bd_bounds[2]:.6f}, MaxY: {bd_bounds[3]:.6f}")

# Periksa apakah ada overlap
x_overlap = (at_bounds[0] <= bd_bounds[2]) and (at_bounds[2] >= bd_bounds[0])
y_overlap = (at_bounds[1] <= bd_bounds[3]) and (at_bounds[3] >= bd_bounds[1])

print(f"\n4. Analisis Overlap:")
print(f"   - X overlap: {x_overlap}")
print(f"   - Y overlap: {y_overlap}")
print(f"   - Area berpotensi overlap: {x_overlap and y_overlap}")

# Tampilkan kolom-kolom dalam batas_desa
print(f"\n5. Kolom dalam batas_desa:")
for col in batas_desa.columns:
    print(f"   - {col}")

# Periksa nilai WADMKK
if 'WADMKK' in batas_desa.columns:
    print(f"\n6. Nilai WADMKK unik:")
    wadmkk_values = batas_desa['WADMKK'].unique()
    for wadmkk in sorted(wadmkk_values):
        count = len(batas_desa[batas_desa['WADMKK'] == wadmkk])
        print(f"   - {wadmkk}: {count} fitur")
else:
    print("\n6. ❌ Kolom WADMKK tidak ditemukan!")

# Coba interseksi dengan CRS yang sudah diseragamkan
print(f"\n7. Test interseksi dengan CRS diseragamkan:")
try:
    intersected = gpd.overlay(area_tanam, batas_desa, how='intersection')
    print(f"   - Hasil interseksi: {len(intersected)} fitur")
    
    if len(intersected) > 0:
        print("   ✓ Ada interseksi setelah menyamakan CRS!")
        
        # Dissolve berdasarkan WADMKK
        if 'WADMKK' in intersected.columns:
            dissolved = intersected.dissolve(by='WADMKK')
            print(f"   - Hasil dissolve: {len(dissolved)} wilayah")
            
            # Simpan hasil
            dissolved.to_file("Area_Tanam_Belitung_BelitungTimur_Fixed.shp")
            print("   ✓ File diperbaiki: Area_Tanam_Belitung_BelitungTimur_Fixed.shp")
        else:
            print("   ❌ Kolom WADMKK tidak ada dalam hasil interseksi")
    else:
        print("   ❌ Masih tidak ada interseksi")
        
except Exception as e:
    print(f"   ❌ Error saat interseksi: {e}")

# Buat visualisasi sederhana untuk melihat posisi data
print(f"\n8. Membuat visualisasi...")
try:
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # Plot batas desa
    batas_desa.plot(ax=ax, color='lightblue', edgecolor='blue', alpha=0.7, label='Batas Desa')
    
    # Plot area tanam
    area_tanam.plot(ax=ax, color='red', alpha=0.8, label='Area Tanam')
    
    ax.legend()
    ax.set_title('Posisi Area Tanam vs Batas Desa')
    plt.tight_layout()
    plt.savefig('diagnosa_posisi.png', dpi=150, bbox_inches='tight')
    print("   ✓ Visualisasi disimpan: diagnosa_posisi.png")
    
except Exception as e:
    print(f"   ❌ Error membuat visualisasi: {e}")

print("\n=== DIAGNOSA SELESAI ===") 