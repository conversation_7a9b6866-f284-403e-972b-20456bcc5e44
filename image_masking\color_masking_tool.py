import os
import numpy as np
import cv2
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser, scrolledtext
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ColorMaskingTool:
    def __init__(self, master):
        self.master = master
        self.master.title("Color-Based Image Masking Tool")
        self.master.geometry("1300x800")
        
        # Initialize variables
        self.image_path = tk.StringVar()
        self.default_image_path = r"D:\Gawean Rebinmas\Geo  Processing GIS Alat\image_to_shapefile\D. Peta Sebaran Areal HCV_page-0001.png"
        
        if os.path.exists(self.default_image_path):
            self.image_path.set(self.default_image_path)
        
        self.selected_colors = []  # List to store (color_rgb, threshold) tuples
        self.original_image = None
        self.displayed_image = None
        self.mask_image = None
        self.combined_mask = None
        self.threshold = tk.IntVar(value=30)  # Default color threshold
        self.current_color = None
        self.mask_mode = tk.StringVar(value="combined")  # "combined", "individual"
        self.color_display_size = 30  # Size of color swatches
        self.picking_color = False
        
        # Create the GUI
        self.create_gui()
        
        # Load default image if available
        if os.path.exists(self.default_image_path):
            self.load_image(self.default_image_path)
        
    def create_gui(self):
        # Create main frame
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel for controls
        left_panel = ttk.Frame(main_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Image selection
        image_frame = ttk.LabelFrame(left_panel, text="Image Selection", padding="10")
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(image_frame, text="Image Path:").pack(anchor=tk.W)
        path_entry = ttk.Entry(image_frame, textvariable=self.image_path, width=40)
        path_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(image_frame, text="Browse...", command=self.browse_image).pack(anchor=tk.W)
        ttk.Button(image_frame, text="Load Image", command=self.load_image_from_path).pack(fill=tk.X, pady=(5, 0))
        
        # Color selection
        color_frame = ttk.LabelFrame(left_panel, text="Color Selection", padding="10")
        color_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(color_frame, text="Pick Color from Image", command=self.start_color_picking).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(color_frame, text="Choose Color from Palette", command=self.open_color_chooser).pack(fill=tk.X)
        
        # Color threshold
        threshold_frame = ttk.Frame(color_frame)
        threshold_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(threshold_frame, text="Color Threshold:").pack(side=tk.LEFT)
        self.threshold_scale = ttk.Scale(threshold_frame, from_=1, to=100, orient=tk.HORIZONTAL, 
                              variable=self.threshold, command=self.update_threshold)
        self.threshold_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.threshold_label = ttk.Label(threshold_frame, text=f"{self.threshold.get()}")
        self.threshold_label.pack(side=tk.LEFT, padx=(0, 5))
        
        # Current color display
        current_color_frame = ttk.Frame(color_frame)
        current_color_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(current_color_frame, text="Current Color:").pack(side=tk.LEFT)
        self.current_color_canvas = tk.Canvas(current_color_frame, width=self.color_display_size, 
                                             height=self.color_display_size, bg="white", bd=1, relief="solid")
        self.current_color_canvas.pack(side=tk.LEFT, padx=(5, 0))
        
        # Selected colors list
        colors_list_frame = ttk.LabelFrame(left_panel, text="Selected Colors", padding="10")
        colors_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create a frame for the color swatches with scrollbar
        color_scroll_frame = ttk.Frame(colors_list_frame)
        color_scroll_frame.pack(fill=tk.BOTH, expand=True)
        
        self.colors_canvas = tk.Canvas(color_scroll_frame)
        scrollbar = ttk.Scrollbar(color_scroll_frame, orient="vertical", command=self.colors_canvas.yview)
        self.scrollable_color_frame = ttk.Frame(self.colors_canvas)
        
        self.scrollable_color_frame.bind(
            "<Configure>",
            lambda e: self.colors_canvas.configure(scrollregion=self.colors_canvas.bbox("all"))
        )
        
        self.colors_canvas.create_window((0, 0), window=self.scrollable_color_frame, anchor="nw")
        self.colors_canvas.configure(yscrollcommand=scrollbar.set)
        
        self.colors_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        ttk.Button(colors_list_frame, text="Clear All Colors", command=self.clear_colors).pack(fill=tk.X, pady=(5, 0))
        
        # Masking options
        mask_frame = ttk.LabelFrame(left_panel, text="Masking Options", padding="10")
        mask_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Radiobutton(mask_frame, text="Combined Mask (OR)", variable=self.mask_mode, 
                       value="combined", command=self.update_mask).pack(anchor=tk.W)
        ttk.Radiobutton(mask_frame, text="Individual Masks", variable=self.mask_mode, 
                       value="individual", command=self.update_mask).pack(anchor=tk.W)
        
        # Export controls
        export_frame = ttk.LabelFrame(left_panel, text="Export", padding="10")
        export_frame.pack(fill=tk.X)
        
        ttk.Button(export_frame, text="Save Mask Image", command=self.save_mask_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(export_frame, text="Save Masked Original Image", command=self.save_masked_original).pack(fill=tk.X)
        
        # Image display frame (right panel)
        right_panel = ttk.Frame(main_frame)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Image tabs (Original, Mask, Masked)
        self.tab_control = ttk.Notebook(right_panel)
        self.tab_control.pack(fill=tk.BOTH, expand=True)
        
        # Original image tab
        self.original_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.original_tab, text="Original Image")
        
        self.original_canvas = tk.Canvas(self.original_tab, bg="white")
        self.original_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Mask image tab
        self.mask_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.mask_tab, text="Mask")
        
        self.mask_canvas = tk.Canvas(self.mask_tab, bg="black")
        self.mask_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Masked result tab
        self.masked_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.masked_tab, text="Masked Result")
        
        self.masked_canvas = tk.Canvas(self.masked_tab, bg="white")
        self.masked_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Set up event bindings
        self.original_canvas.bind("<Button-1>", self.on_image_click)
        
        # Add status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.master, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def browse_image(self):
        filename = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.tif *.tiff"), ("All files", "*.*")]
        )
        if filename:
            self.image_path.set(filename)
            self.load_image(filename)
    
    def load_image_from_path(self):
        path = self.image_path.get()
        if path and os.path.exists(path):
            self.load_image(path)
        else:
            messagebox.showerror("Error", "Invalid image path")
    
    def load_image(self, path):
        try:
            # Try loading with PIL first (more reliable)
            try:
                pil_image = Image.open(path)
                # Convert PIL to numpy array
                np_image = np.array(pil_image)
                # If image has alpha channel, remove it
                if np_image.shape[2] == 4:
                    np_image = np_image[:, :, :3]
                # Convert RGB to BGR for OpenCV
                self.original_image = np_image[:, :, ::-1].copy()
            except Exception as pil_error:
                logger.warning(f"Failed to load with PIL: {str(pil_error)}, trying OpenCV")
                # Fallback to OpenCV
                self.original_image = cv2.imread(path)
            
            if self.original_image is None or self.original_image.size == 0:
                raise ValueError(f"Failed to load image from {path}")
            
            # Verify image dimensions
            if len(self.original_image.shape) < 2:
                raise ValueError(f"Invalid image format from {path}")
            
            # Convert to RGB for display
            if len(self.original_image.shape) == 3:
                self.displayed_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
            else:
                # Grayscale image
                self.displayed_image = cv2.cvtColor(self.original_image, cv2.COLOR_GRAY2RGB)
            
            # Initialize mask with same dimensions as original image
            self.combined_mask = np.zeros(self.original_image.shape[:2], dtype=np.uint8)
            
            # Update the image display
            self.update_images()
            
            # Update status
            self.status_var.set(f"Loaded image: {path}")
            logger.info(f"Loaded image: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            logger.error(f"Failed to load image: {str(e)}", exc_info=True)
    
    def update_images(self):
        """Update all image displays."""
        if self.original_image is None:
            return
        
        # Update original image
        self.display_image_on_canvas(self.displayed_image, self.original_canvas)
        
        # Update mask image
        self.update_mask()
        
        # Update masked result
        self.update_masked_result()
    
    def display_image_on_canvas(self, image, canvas):
        """Display an image on a canvas, resizing to fit."""
        if image is None:
            return
        
        # Get canvas dimensions
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()
        
        # If canvas not yet realized, use a default size
        if canvas_width < 10:
            canvas_width = 600
        if canvas_height < 10:
            canvas_height = 400
        
        # Calculate scale to fit image in canvas while maintaining aspect ratio
        img_height, img_width = image.shape[:2]
        
        # Ensure we don't divide by zero
        if img_width == 0 or img_height == 0:
            logger.error("Invalid image dimensions (zero width or height)")
            return
        
        # Calculate scale
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y)
        
        # Ensure scale is positive and not too small
        if scale <= 0.01:
            scale = 0.01
        
        new_width = max(1, int(img_width * scale))
        new_height = max(1, int(img_height * scale))
        
        try:
            # Resize image using PIL instead of OpenCV
            pil_image = Image.fromarray(image.astype('uint8'))
            resized_pil = pil_image.resize((new_width, new_height), Image.BILINEAR)
            
            # Convert directly to PhotoImage
            tk_image = ImageTk.PhotoImage(resized_pil)
            
            # Store reference to prevent garbage collection
            canvas.image = tk_image
            
            # Clear canvas and display image
            canvas.delete("all")
            canvas.create_image(canvas_width//2, canvas_height//2, image=tk_image, anchor=tk.CENTER)
            
            # Store the scale factor for click coordinate conversion
            canvas.scale_factor = scale
            
        except Exception as e:
            logger.error(f"Error resizing image: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to display image: {str(e)}")
    
    def start_color_picking(self):
        """Start color picking mode."""
        self.picking_color = True
        self.status_var.set("Click on the image to pick a color")
    
    def on_image_click(self, event):
        """Handle click on the image."""
        if not self.picking_color or self.original_image is None:
            return
        
        # Get canvas dimensions
        canvas_width = self.original_canvas.winfo_width()
        canvas_height = self.original_canvas.winfo_height()
        
        # Get image dimensions
        img_height, img_width = self.original_image.shape[:2]
        
        # Calculate scale
        scale = getattr(self.original_canvas, 'scale_factor', 1.0)
        
        # Calculate centered position
        img_displayed_width = int(img_width * scale)
        img_displayed_height = int(img_height * scale)
        
        # Calculate offset
        offset_x = (canvas_width - img_displayed_width) // 2
        offset_y = (canvas_height - img_displayed_height) // 2
        
        # Get click coordinates relative to image
        x_on_displayed = event.x - offset_x
        y_on_displayed = event.y - offset_y
        
        # Convert back to original image coordinates
        x_on_original = int(x_on_displayed / scale)
        y_on_original = int(y_on_displayed / scale)
        
        # Make sure coordinates are within image bounds
        if 0 <= x_on_original < img_width and 0 <= y_on_original < img_height:
            # Get the color at clicked point (in RGB format)
            try:
                color_bgr = self.original_image[y_on_original, x_on_original]
                
                # Handle grayscale images
                if isinstance(color_bgr, np.ndarray) and len(color_bgr) >= 3:
                    color_rgb = color_bgr[::-1]  # Convert BGR to RGB
                else:
                    # For grayscale, create an RGB tuple with same values
                    gray_value = int(color_bgr)
                    color_rgb = (gray_value, gray_value, gray_value)
                
                self.current_color = color_rgb
                
                # Update current color display
                self.update_current_color_display()
                
                # Add the color to selected colors
                self.add_selected_color(color_rgb)
                
                self.status_var.set(f"Picked color RGB: {color_rgb}")
                logger.info(f"Picked color RGB: {color_rgb}")
            except Exception as e:
                logger.error(f"Error picking color: {str(e)}", exc_info=True)
                messagebox.showerror("Error", f"Failed to pick color: {str(e)}")
            
            # Exit color picking mode
            self.picking_color = False
    
    def open_color_chooser(self):
        """Open color chooser dialog."""
        color_rgb = colorchooser.askcolor(title="Choose Color")
        if color_rgb[0]:  # [0] is RGB, [1] is hex
            self.current_color = [int(c) for c in color_rgb[0]]
            self.update_current_color_display()
            self.add_selected_color(self.current_color)
    
    def update_current_color_display(self):
        """Update the current color display."""
        if self.current_color is not None:
            # Convert RGB to hex
            if len(self.current_color) >= 3:
                color_hex = "#{:02x}{:02x}{:02x}".format(*self.current_color[:3])
                self.current_color_canvas.config(bg=color_hex)
    
    def add_selected_color(self, color_rgb):
        """Add a color to the selected colors list."""
        if color_rgb is None:
            return
        
        # Get current threshold
        threshold_value = self.threshold.get()
        
        # Add to selected colors
        self.selected_colors.append((color_rgb, threshold_value))
        
        # Update the colors list display
        self.update_colors_list()
        
        # Update the mask
        self.update_mask()
    
    def update_colors_list(self):
        """Update the display of selected colors."""
        # Clear existing children
        for widget in self.scrollable_color_frame.winfo_children():
            widget.destroy()
        
        # Display each selected color
        for i, (color, threshold) in enumerate(self.selected_colors):
            # Create a frame for this color
            color_row = ttk.Frame(self.scrollable_color_frame)
            color_row.pack(fill=tk.X, pady=2)
            
            # Color number label
            ttk.Label(color_row, text=f"{i+1}:").pack(side=tk.LEFT, padx=(0, 5))
            
            # Color swatch
            if len(color) >= 3:
                color_hex = "#{:02x}{:02x}{:02x}".format(*color[:3])
            else:
                color_hex = "#ffffff"  # Default to white if color is invalid
                
            color_swatch = tk.Canvas(color_row, width=self.color_display_size, height=self.color_display_size, 
                                    bg=color_hex, bd=1, relief="solid")
            color_swatch.pack(side=tk.LEFT, padx=(0, 5))
            
            # RGB value
            ttk.Label(color_row, text=f"RGB: {color}").pack(side=tk.LEFT)
            
            # Threshold value
            ttk.Label(color_row, text=f"Threshold: {threshold}").pack(side=tk.LEFT, padx=(5, 0))
            
            # Remove button
            remove_btn = ttk.Button(color_row, text="✕", width=3, 
                                  command=lambda idx=i: self.remove_color(idx))
            remove_btn.pack(side=tk.RIGHT)
    
    def remove_color(self, index):
        """Remove a color from the selected colors list."""
        if 0 <= index < len(self.selected_colors):
            del self.selected_colors[index]
            self.update_colors_list()
            self.update_mask()
    
    def clear_colors(self):
        """Clear all selected colors."""
        self.selected_colors = []
        self.update_colors_list()
        self.update_mask()
    
    def update_threshold(self, *args):
        """Update the threshold value display."""
        self.threshold_label.config(text=f"{self.threshold.get()}")
    
    def update_mask(self):
        """Update the mask based on selected colors."""
        if self.original_image is None or not self.selected_colors:
            return
        
        try:
            # Reset combined mask
            h, w = self.original_image.shape[:2]
            self.combined_mask = np.zeros((h, w), dtype=np.uint8)
            
            # Create masks for each selected color
            masks = []
            for color_rgb, threshold in self.selected_colors:
                try:
                    # Convert target color to BGR for OpenCV
                    if len(color_rgb) >= 3:
                        color_bgr = (color_rgb[2], color_rgb[1], color_rgb[0])
                    else:
                        # Handle grayscale case
                        color_bgr = color_rgb
                    
                    # Create mask for this color
                    if len(self.original_image.shape) == 3:  # Color image
                        lower_bound = np.array([max(0, c - threshold) for c in color_bgr], dtype=np.uint8)
                        upper_bound = np.array([min(255, c + threshold) for c in color_bgr], dtype=np.uint8)
                        mask = cv2.inRange(self.original_image, lower_bound, upper_bound)
                    else:  # Grayscale image
                        if isinstance(color_bgr, (list, tuple, np.ndarray)):
                            # Use the first value for grayscale
                            gray_val = color_bgr[0] if len(color_bgr) > 0 else 0
                        else:
                            gray_val = color_bgr
                        lower = max(0, gray_val - threshold)
                        upper = min(255, gray_val + threshold)
                        mask = cv2.inRange(self.original_image, lower, upper)
                    
                    masks.append(mask)
                    
                    # Update combined mask (OR operation)
                    self.combined_mask = cv2.bitwise_or(self.combined_mask, mask)
                except Exception as e:
                    logger.error(f"Error creating mask for color {color_rgb}: {str(e)}", exc_info=True)
            
            # If in individual mode, display all masks in a grid
            if self.mask_mode.get() == "individual" and masks:
                # Determine the grid size (roughly square)
                n = len(masks)
                cols = max(1, int(np.ceil(np.sqrt(n))))
                rows = max(1, int(np.ceil(n / cols)))
                
                # Create a composite image
                cell_height = max(1, h // rows)
                cell_width = max(1, w // cols)
                composite = np.zeros((cell_height * rows, cell_width * cols), dtype=np.uint8)
                
                for i, mask in enumerate(masks):
                    if i < rows * cols:  # Safety check
                        r, c = i // cols, i % cols
                        
                        # Resize the mask to fit the cell
                        try:
                            resized_mask = cv2.resize(mask, (cell_width, cell_height), 
                                                    interpolation=cv2.INTER_NEAREST)
                            
                            # Place in the composite image
                            composite[r*cell_height:(r+1)*cell_height, c*cell_width:(c+1)*cell_width] = resized_mask
                        except Exception as e:
                            logger.error(f"Error resizing mask: {str(e)}", exc_info=True)
                
                # Display the composite image
                self.display_image_on_canvas(composite, self.mask_canvas)
            else:
                # Display the combined mask
                self.display_image_on_canvas(self.combined_mask, self.mask_canvas)
            
            # Update masked result
            self.update_masked_result()
        except Exception as e:
            logger.error(f"Error updating mask: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to update mask: {str(e)}")
    
    def update_masked_result(self):
        """Update the masked result image."""
        if self.original_image is None or self.combined_mask is None:
            return
        
        try:
            # Apply mask to original image
            masked_image = self.original_image.copy()
            
            # For grayscale images
            if len(self.original_image.shape) == 2:
                # Convert to 3 channels for display
                masked_image = cv2.cvtColor(masked_image, cv2.COLOR_GRAY2BGR)
            
            # Apply the mask
            masked_image = cv2.bitwise_and(masked_image, masked_image, mask=self.combined_mask)
            
            # Convert to RGB for display
            masked_image_rgb = cv2.cvtColor(masked_image, cv2.COLOR_BGR2RGB)
            
            # Display the masked result
            self.display_image_on_canvas(masked_image_rgb, self.masked_canvas)
        except Exception as e:
            logger.error(f"Error updating masked result: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to update masked result: {str(e)}")
    
    def save_mask_image(self):
        """Save the mask image to a file."""
        if self.combined_mask is None:
            messagebox.showerror("Error", "No mask to save")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Save Mask Image",
            defaultextension=".png",
            filetypes=[("PNG Image", "*.png"), ("TIFF Image", "*.tif")]
        )
        
        if filename:
            try:
                cv2.imwrite(filename, self.combined_mask)
                self.status_var.set(f"Saved mask to {filename}")
                logger.info(f"Saved mask to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save mask: {str(e)}")
                logger.error(f"Failed to save mask: {str(e)}", exc_info=True)
    
    def save_masked_original(self):
        """Save the masked original image to a file."""
        if self.original_image is None or self.combined_mask is None:
            messagebox.showerror("Error", "No masked image to save")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Save Masked Image",
            defaultextension=".png",
            filetypes=[("PNG Image", "*.png"), ("JPEG Image", "*.jpg"),
                      ("TIFF Image", "*.tif")]
        )
        
        if filename:
            try:
                # Apply mask to original image
                masked_image = self.original_image.copy()
                
                # For grayscale images
                if len(self.original_image.shape) == 2:
                    # Convert to 3 channels for saving
                    masked_image = cv2.cvtColor(masked_image, cv2.COLOR_GRAY2BGR)
                
                # Apply the mask
                masked_image = cv2.bitwise_and(masked_image, masked_image, mask=self.combined_mask)
                
                cv2.imwrite(filename, masked_image)
                self.status_var.set(f"Saved masked image to {filename}")
                logger.info(f"Saved masked image to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save masked image: {str(e)}")
                logger.error(f"Failed to save masked image: {str(e)}", exc_info=True)

if __name__ == "__main__":
    # Create Tkinter root window
    root = tk.Tk()
    
    # Create the application
    app = ColorMaskingTool(root)
    
    # Start the GUI event loop
    root.mainloop() 