#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Update Inclave Areas

This script runs the update_inclave_areas.py script to update the inclave areas
in a shapefile. It provides a simple command-line interface to select the input
shapefile and specify the output file.

Author: AI Assistant
Date: 2023-05-14
Updated: 2025-05-20
"""

import os
import sys
import tkinter as tk
from tkinter import filedialog
from update_inclave_areas import InclaveAreaUpdater, logger

def main():
    """Main function to run the inclave area updater."""
    # Create a simple GUI to select the input file
    root = tk.Tk()
    root.title("Update Inclave Areas")
    root.geometry("500x300")
    
    # Create a label with instructions
    label = tk.Label(root, text="This tool updates inclave areas in a shapefile.\n"
                              "It recalculates the total inclave area for each boundary\n"
                              "based on the SUB_DIVISI and BLOK values.", 
                    font=("Arial", 12))
    label.pack(pady=20)
    
    # Function to handle the file selection and processing
    def process_file():
        input_file = filedialog.askopenfilename(
            title="Select Shapefile to Update",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        
        if not input_file:
            status_label.config(text="No input file selected.")
            return
            
        output_file = filedialog.asksaveasfilename(
            title="Save Updated Shapefile As",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
            defaultextension=".shp"
        )
        
        if not output_file:
            status_label.config(text="No output file selected. Using default.")
            # Create a default output filename based on the input
            basename = os.path.splitext(os.path.basename(input_file))[0]
            dirname = os.path.dirname(input_file)
            output_file = os.path.join(dirname, f"{basename}_updated.shp")
        
        # Update status
        status_label.config(text=f"Processing {os.path.basename(input_file)}...")
        root.update()
        
        # Create the updater and process the shapefile
        updater = InclaveAreaUpdater(input_file)
        
        if updater.load_shapefile():
            if updater.update_areas():
                updater.generate_report()
                if updater.save_updated_shapefile(output_file):
                    # Generate a visualization of the changes
                    viz_output = os.path.splitext(output_file)[0] + "_visualization.png"
                    updater.visualize_changes(viz_output)
                    
                    status_label.config(text=f"Successfully updated {os.path.basename(input_file)}.\n"
                                           f"Saved to {os.path.basename(output_file)}.")
                else:
                    status_label.config(text="Failed to save updated shapefile.")
            else:
                status_label.config(text="Failed to update areas. Check the log for details.")
        else:
            status_label.config(text="Failed to load the shapefile. Check the log for details.")
    
    # Create buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    process_button = tk.Button(button_frame, text="Select and Process Shapefile", command=process_file,
                              font=("Arial", 11), padx=10, pady=5)
    process_button.pack(side=tk.LEFT, padx=10)
    
    exit_button = tk.Button(button_frame, text="Exit", command=root.destroy,
                           font=("Arial", 11), padx=10, pady=5)
    exit_button.pack(side=tk.LEFT, padx=10)
    
    # Status label
    status_label = tk.Label(root, text="Ready to process shapefile.", font=("Arial", 10))
    status_label.pack(pady=20)
    
    # Run the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
