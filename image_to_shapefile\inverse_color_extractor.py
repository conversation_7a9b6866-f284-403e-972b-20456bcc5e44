import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, Scale
import numpy as np
import cv2
from PIL import Image, ImageTk
import geopandas as gpd
from shapely.geometry import Polygon, MultiPolygon, Point
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.colors as mcolors
from collections import deque
import math
from scipy.interpolate import griddata

class InverseColorExtractorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Inverse Color Extractor - Select Colors to Exclude")
        self.root.geometry("1400x900")

        # Initialize variables
        self.image_path = None
        self.config_path = None
        self.original_image = None
        self.cv_image = None
        self.mask = None
        self.contours = None
        self.polygons = []
        self.georef_data = None
        self.scale_factor = 1.0
        self.transform_matrix = None  # For perspective transformation
        self.georef_points = None  # For storing georef points

        # Store tk PhotoImage references to prevent garbage collection
        self.photo_images = {}  # Dictionary to store all PhotoImage objects

        # HSV range variables
        self.h_min = tk.IntVar(value=0)
        self.h_max = tk.IntVar(value=179)
        self.s_min = tk.IntVar(value=0)
        self.s_max = tk.IntVar(value=255)
        self.v_min = tk.IntVar(value=0)
        self.v_max = tk.IntVar(value=255)

        # Polygon simplification variables
        self.epsilon_factor = tk.DoubleVar(value=0.001)
        self.min_area = tk.IntVar(value=100)

        # Color sampling variables
        self.excluded_color_samples = []  # List of (h, s, v) tuples to exclude
        self.sample_tolerance = tk.IntVar(value=10)  # Tolerance for color matching
        self.sampling_mode = False  # Flag to indicate if we're in sampling mode
        self.excluded_mask = None  # Mask of excluded colors

        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a frame with scrollbar for the left panel
        self.control_outer_frame = ttk.Frame(self.main_frame)
        self.control_outer_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Create a canvas with scrollbar
        self.control_canvas = tk.Canvas(self.control_outer_frame, width=280)
        self.control_scrollbar = ttk.Scrollbar(self.control_outer_frame, orient="vertical", command=self.control_canvas.yview)

        # Pack the scrollbar and canvas
        self.control_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.control_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.control_canvas.configure(yscrollcommand=self.control_scrollbar.set)

        # Create the frame for controls inside the canvas
        self.control_frame = ttk.Frame(self.control_canvas)
        self.control_window = self.control_canvas.create_window((0, 0), window=self.control_frame, anchor="nw")

        # Configure the canvas to resize the window when the frame changes size
        def configure_control_frame(event):
            self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
            self.control_canvas.itemconfig(self.control_window, width=event.width)

        self.control_frame.bind("<Configure>", configure_control_frame)

        # Bind mousewheel to scroll
        self.control_canvas.bind_all("<MouseWheel>", self.on_mousewheel)

        # Create right panel for image display
        self.image_frame = ttk.LabelFrame(self.main_frame, text="Image Preview")
        self.image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a color preview box for sampling
        self.color_preview = tk.Canvas(self.root, width=30, height=30, bg="white", highlightthickness=1, highlightbackground="black")
        self.color_preview.place(x=-100, y=-100)  # Place it off-screen initially

        # Create notebook for different views
        self.notebook = ttk.Notebook(self.image_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Original image tab
        self.original_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.original_tab, text="Original Image")

        # Mask preview tab
        self.mask_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.mask_tab, text="Mask (Non-Excluded)")

        # Excluded colors tab
        self.excluded_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.excluded_tab, text="Excluded Colors")

        # Contours preview tab
        self.contour_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.contour_tab, text="Contours")

        # Georeferenced view tab
        self.georef_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.georef_tab, text="Georeferenced View")

        # Setup scrollable canvases for each tab
        self.setup_scrollable_canvas(self.original_tab, "original_canvas")
        self.setup_scrollable_canvas(self.mask_tab, "mask_canvas")
        self.setup_scrollable_canvas(self.excluded_tab, "excluded_canvas")
        self.setup_scrollable_canvas(self.contour_tab, "contour_canvas")
        self.setup_scrollable_canvas(self.georef_tab, "georef_canvas")

        # Bind click event to original canvas for color sampling
        self.original_canvas.bind("<Button-1>", self.on_canvas_click)

        # Add status label for mouse position and color info
        self.color_info_var = tk.StringVar()
        self.color_info_label = ttk.Label(self.original_tab, textvariable=self.color_info_var,
                                         relief=tk.SUNKEN, anchor=tk.W)
        self.color_info_label.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind mouse movement to show color info
        self.original_canvas.bind("<Motion>", self.on_canvas_move)

        # Bind mouse movement on georef canvas to show coordinates
        self.georef_canvas.bind("<Motion>", self.on_georef_canvas_move)

        # Add control elements
        self.create_control_panel()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Set initial status
        self.status_var.set("Ready. Load an image and georeferencing config to begin.")

    def setup_scrollable_canvas(self, parent, canvas_name):
        """Create a scrollable canvas with both horizontal and vertical scrollbars."""
        # Create a frame to hold the canvas and scrollbars
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True)

        # Create canvas
        canvas = tk.Canvas(frame, bg="white")
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add scrollbars
        h_scrollbar = ttk.Scrollbar(frame, orient=tk.HORIZONTAL, command=canvas.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        v_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=canvas.yview)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure canvas
        canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # Store canvas as an attribute of the class
        setattr(self, canvas_name, canvas)

        return canvas

    def create_control_panel(self):
        # Add a title for the control panel
        title_label = ttk.Label(self.control_frame, text="Inverse Color Extractor", font=("Arial", 12, "bold"))
        title_label.pack(fill=tk.X, padx=5, pady=5)

        description = ttk.Label(self.control_frame, text="Select colors to EXCLUDE from the mask",
                               wraplength=250, justify=tk.LEFT)
        description.pack(fill=tk.X, padx=5, pady=5)

        # File controls
        file_frame = ttk.LabelFrame(self.control_frame, text="Files")
        file_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(file_frame, text="Load Image", command=self.load_image).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(file_frame, text="Load Georef Config", command=self.load_config).pack(fill=tk.X, padx=5, pady=5)

        # Quick load for 4-point config
        ttk.Button(file_frame, text="Load 4-Point Config",
                  command=lambda: self.load_config_from_path("map_georef_config_4_titik.json")).pack(fill=tk.X, padx=5, pady=5)

        # Georeferencing info
        self.georef_info_frame = ttk.LabelFrame(self.control_frame, text="Georeferencing Info")
        self.georef_info_frame.pack(fill=tk.X, padx=5, pady=5)

        self.georef_info_text = tk.Text(self.georef_info_frame, height=4, width=30, wrap=tk.WORD)
        self.georef_info_text.pack(fill=tk.X, padx=5, pady=5)
        self.georef_info_text.insert(tk.END, "No georeferencing loaded.")
        self.georef_info_text.config(state=tk.DISABLED)

        # Color sampling controls
        sampling_frame = ttk.LabelFrame(self.control_frame, text="Color Sampling (Exclusion)")
        sampling_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(sampling_frame, text="Start Color Sampling",
                  command=self.start_color_sampling).pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(sampling_frame, text="Sample Tolerance:").pack(anchor=tk.W, padx=5, pady=2)
        Scale(sampling_frame, from_=1, to=50, orient=tk.HORIZONTAL,
              variable=self.sample_tolerance, label="Tolerance",
              command=lambda _: self.update_preview_if_samples()).pack(fill=tk.X, padx=5, pady=2)

        # Color samples display
        samples_frame = ttk.LabelFrame(self.control_frame, text="Excluded Color Samples")
        samples_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create a frame for the samples list
        self.samples_list_frame = ttk.Frame(samples_frame)
        self.samples_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a canvas to display color swatches
        self.samples_canvas = tk.Canvas(self.samples_list_frame, height=120, bg="white")
        self.samples_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add a scrollbar to the canvas
        samples_scrollbar = ttk.Scrollbar(self.samples_list_frame, orient=tk.VERTICAL,
                                         command=self.samples_canvas.yview)
        samples_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.samples_canvas.configure(yscrollcommand=samples_scrollbar.set)

        # Create a frame inside the canvas to hold the color samples
        self.samples_inner_frame = ttk.Frame(self.samples_canvas)
        self.samples_window = self.samples_canvas.create_window((0, 0), window=self.samples_inner_frame, anchor="nw")

        # Configure the canvas to resize the window when the frame changes size
        def configure_samples_frame(event):
            self.samples_canvas.configure(scrollregion=self.samples_canvas.bbox("all"))
            self.samples_canvas.itemconfig(self.samples_window, width=event.width)

        self.samples_inner_frame.bind("<Configure>", configure_samples_frame)

        # Create a listbox to track samples (hidden, just for selection management)
        self.samples_listbox = tk.Listbox(self.root)

        # Add buttons for managing samples
        samples_buttons_frame = ttk.Frame(samples_frame)
        samples_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(samples_buttons_frame, text="Remove Selected",
                  command=self.remove_selected_sample).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
        ttk.Button(samples_buttons_frame, text="Clear All",
                  command=self.clear_all_samples).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=2)

        # Polygon controls
        polygon_frame = ttk.LabelFrame(self.control_frame, text="Polygon Settings")
        polygon_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(polygon_frame, text="Simplification Factor:").pack(anchor=tk.W, padx=5, pady=2)
        Scale(polygon_frame, from_=0.0001, to=0.01, resolution=0.0001, orient=tk.HORIZONTAL,
              variable=self.epsilon_factor, label="Epsilon",
              command=lambda _: self.update_preview()).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(polygon_frame, text="Minimum Area (pixels²):").pack(anchor=tk.W, padx=5, pady=2)
        Scale(polygon_frame, from_=10, to=1000, orient=tk.HORIZONTAL,
              variable=self.min_area, label="Min Area",
              command=lambda _: self.update_preview()).pack(fill=tk.X, padx=5, pady=2)

        # Action buttons
        action_frame = ttk.LabelFrame(self.control_frame, text="Actions")
        action_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(action_frame, text="Update Preview", command=self.update_preview).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(action_frame, text="Export to Shapefile", command=self.export_to_shapefile).pack(fill=tk.X, padx=5, pady=5)

    def load_image(self):
        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.tif;*.tiff"), ("All files", "*.*")]
        )
        if file_path:
            self.image_path = file_path
            self.load_image_from_path(file_path)

    def load_image_from_path(self, path):
        try:
            # Reset previous image references
            self.original_image = None
            self.cv_image = None
            self.photo_images = {}

            # Clear canvases
            for canvas in [self.original_canvas, self.mask_canvas, self.excluded_canvas, self.contour_canvas, self.georef_canvas]:
                canvas.delete("all")

            # Update status
            self.status_var.set(f"Loading image: {path}")
            self.root.update_idletasks()  # Update the UI

            # Load with PIL for display
            try:
                self.original_image = Image.open(path)
                # Convert RGBA to RGB if needed
                if self.original_image.mode == 'RGBA':
                    self.original_image = self.original_image.convert('RGB')
                    print(f"Converted RGBA image to RGB mode")

                # Keep a copy of the original image to prevent garbage collection
                self.pil_image_copy = self.original_image.copy()

                # Display original image first
                self.display_image(self.original_image, self.original_canvas)

                # Update to show progress
                self.status_var.set(f"Image loaded: {path}")
                self.root.update_idletasks()
            except Exception as e:
                messagebox.showerror("PIL Error", f"Failed to load image with PIL: {str(e)}")
                return

            # Load with OpenCV for processing (try both direct and via PIL)
            try:
                # Try direct OpenCV loading first
                self.cv_image = cv2.imread(path)

                # If failed, try through PIL
                if self.cv_image is None or self.cv_image.size == 0:
                    self.status_var.set("Using PIL to load image for processing...")
                    pil_image = self.original_image.convert('RGB')
                    self.cv_image = np.array(pil_image)
                    # Convert RGB to BGR (OpenCV format)
                    self.cv_image = cv2.cvtColor(self.cv_image, cv2.COLOR_RGB2BGR)

                # Convert back to RGB for our processing
                self.cv_image = cv2.cvtColor(self.cv_image, cv2.COLOR_BGR2RGB)

                # Store image path
                self.image_path = path

                # Update status
                self.status_var.set(f"Image loaded and ready: {path}")

            except Exception as e:
                messagebox.showerror("OpenCV Error", f"Failed to process image: {str(e)}")
                # Still keep the displayed image
                self.status_var.set(f"Image displayed but processing failed: {str(e)}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.status_var.set(f"Error loading image: {str(e)}")

    def load_config(self):
        file_path = filedialog.askopenfilename(
            title="Select Georeferencing Config File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.config_path = file_path
            self.load_config_from_path(file_path)

    def load_config_from_path(self, path):
        try:
            with open(path, 'r') as f:
                self.georef_data = json.load(f)

            # Check if the config has the required fields
            if not all(key in self.georef_data for key in ["image_path", "image_width", "image_height", "points"]):
                raise ValueError("Invalid georeferencing config file format")

            # Check if we have at least 4 points for robust georeferencing
            if len(self.georef_data["points"]) < 4:
                messagebox.showwarning("Warning", f"Only {len(self.georef_data['points'])} points found in config. 4 points recommended for best results.")
            else:
                messagebox.showinfo("Success", f"Loaded {len(self.georef_data['points'])} georeferencing points!")

            # If the image hasn't been loaded yet, try to load it from the config
            if self.original_image is None and os.path.exists(self.georef_data["image_path"]):
                self.image_path = self.georef_data["image_path"]
                self.load_image_from_path(self.image_path)

            # Set up the transformation matrix
            self.setup_transform_matrix()

            # Update georef info display
            self.update_georef_info()

            # Update the georeferenced view
            self.update_georef_display()

            self.status_var.set(f"Loaded georeferencing config: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load config: {str(e)}")
            self.status_var.set(f"Error loading config: {str(e)}")

    def update_georef_info(self):
        """Update the georeferencing information display."""
        if not self.georef_data:
            return

        self.georef_info_text.config(state=tk.NORMAL)
        self.georef_info_text.delete(1.0, tk.END)

        text = f"Image: {os.path.basename(self.georef_data['image_path'])}\n"
        text += f"Points: {len(self.georef_data['points'])}\n"

        if len(self.georef_data["points"]) > 0:
            # Calculate bounding box of reference points
            lats = [p["latitude"] for p in self.georef_data["points"]]
            lons = [p["longitude"] for p in self.georef_data["points"]]
            min_lat, max_lat = min(lats), max(lats)
            min_lon, max_lon = min(lons), max(lons)

            text += f"Bounds: {min_lat:.4f},{min_lon:.4f} to {max_lat:.4f},{max_lon:.4f}"

        self.georef_info_text.insert(tk.END, text)
        self.georef_info_text.config(state=tk.DISABLED)

    def setup_transform_matrix(self):
        """Set up the transformation matrix for coordinate conversion."""
        if not self.georef_data or not self.georef_data["points"] or len(self.georef_data["points"]) < 4:
            self.transform_matrix = None
            return

        try:
            # Store the reference points for later use
            self.georef_points = self.georef_data["points"]

            # For more complex transformations, we'll use the 4+ points directly
            # when transforming coordinates, rather than computing a single matrix
            self.transform_matrix = True  # Just a flag to indicate we have reference points

            self.status_var.set(f"Transformation set up with {len(self.georef_points)} points")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to set up transformation: {str(e)}")
            self.transform_matrix = None

    def update_georef_display(self):
        """Update the georeferenced view display."""
        # This would show the image with georeferencing grid or overlay
        # For now, just display the original image
        if self.original_image:
            self.display_image(self.original_image, self.georef_canvas)

    def display_image(self, image, canvas):
        """Display an image on a canvas with proper scaling."""
        if image is None:
            self.status_var.set("Cannot display: image is None")
            return

        try:
            # Make sure we have a PIL Image
            if not isinstance(image, Image.Image):
                # Try to convert numpy array to PIL Image
                if isinstance(image, np.ndarray):
                    # Convert if it's in BGR format (OpenCV default)
                    if len(image.shape) == 3 and image.shape[2] == 3:  # RGB or BGR
                        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    image = Image.fromarray(image.astype('uint8'))
                else:
                    self.status_var.set(f"Cannot display image of type {type(image)}")
                    print(f"Cannot display: received {type(image)}, need PIL Image or numpy array")
                    return

            # Resize image based on scale factor
            width = int(image.width * self.scale_factor)
            height = int(image.height * self.scale_factor)

            # Try with safer resampling method
            try:
                resized_image = image.resize((width, height), Image.LANCZOS)
            except Exception:
                # Fallback to simpler resampling method
                resized_image = image.resize((width, height), Image.NEAREST)

            # Convert to RGB mode if not already
            if resized_image.mode != 'RGB':
                resized_image = resized_image.convert('RGB')

            # Create PhotoImage and store in our dictionary
            canvas_name = str(canvas)
            self.photo_images[canvas_name] = ImageTk.PhotoImage(resized_image)

            # Update canvas
            canvas.delete("all")
            canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_images[canvas_name])
            canvas.config(scrollregion=canvas.bbox(tk.ALL))

            # Update status
            self.status_var.set(f"Image displayed on {canvas_name}")
        except Exception as e:
            self.status_var.set(f"Error displaying image: {str(e)}")
            print(f"Display error: {str(e)}")

    def on_mousewheel(self, event):
        """Handle mouse wheel events to scroll the canvas."""
        self.control_canvas.yview_scroll(-1 * (event.delta // 120), "units")

    def on_canvas_move(self, event):
        """Handle mouse movement to show color information and coordinates."""
        if self.cv_image is None:
            return

        # Get canvas coordinates
        canvas_x = self.original_canvas.canvasx(event.x)
        canvas_y = self.original_canvas.canvasy(event.y)

        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)

        # Check if coordinates are within image bounds
        if 0 <= original_x < self.cv_image.shape[1] and 0 <= original_y < self.cv_image.shape[0]:
            # Get RGB color at the clicked point
            rgb_color = self.cv_image[original_y, original_x]

            # Convert to HSV
            hsv_color = cv2.cvtColor(np.uint8([[rgb_color]]), cv2.COLOR_RGB2HSV)[0][0]

            # Get geographic coordinates if available
            geo_info = ""
            if self.georef_points:
                try:
                    lat, lon = self.pixel_to_geo(original_x, original_y)
                    if lat is not None and lon is not None:
                        geo_info = f" | Geo: {lat:.6f}, {lon:.6f}"
                except Exception:
                    pass

            # Update color info label with both pixel and geographic coordinates
            self.color_info_var.set(f"Pixel: ({original_x}, {original_y}) | HSV: {hsv_color[0]}, {hsv_color[1]}, {hsv_color[2]}{geo_info}")

            # If in sampling mode, update the color preview
            if self.sampling_mode:
                # Update color preview position
                self.color_preview.place(x=event.x_root - 15, y=event.y_root - 15)

                # Update color preview color
                self.color_preview.config(bg=f"#{rgb_color[0]:02x}{rgb_color[1]:02x}{rgb_color[2]:02x}")

    def on_georef_canvas_move(self, event):
        """Show coordinates when mouse moves over the georeferenced view."""
        if self.cv_image is None:
            return

        # Get canvas coordinates
        canvas_x = self.georef_canvas.canvasx(event.x)
        canvas_y = self.georef_canvas.canvasy(event.y)

        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)

        # Check if coordinates are within image bounds
        if 0 <= original_x < self.cv_image.shape[1] and 0 <= original_y < self.cv_image.shape[0]:
            geo_info = "No georeferencing data"

            if self.georef_points:
                try:
                    lat, lon = self.pixel_to_geo(original_x, original_y)
                    if lat is not None and lon is not None:
                        geo_info = f"Lat: {lat:.6f}, Lon: {lon:.6f} | Pixel: ({original_x}, {original_y})"
                except Exception:
                    geo_info = f"Could not calculate coordinates | Pixel: ({original_x}, {original_y})"

            # Add coordinate info label to georef tab if not exists
            if not hasattr(self, 'georef_info_label'):
                self.georef_info_var = tk.StringVar()
                self.georef_info_label = ttk.Label(self.georef_tab, textvariable=self.georef_info_var,
                                                relief=tk.SUNKEN, anchor=tk.W)
                self.georef_info_label.pack(side=tk.BOTTOM, fill=tk.X)

            # Update the info label
            self.georef_info_var.set(geo_info)

    def pixel_to_geo(self, x, y):
        """Convert pixel coordinates to geographic coordinates using perspective transformation with 4 control points."""
        if not self.georef_points or len(self.georef_points) < 4:
            messagebox.showwarning("Warning", "Need at least 4 control points for accurate transformation.")
            return None, None

        try:
            # Get source points (pixel coordinates) and destination points (geographic coordinates)
            src_points = np.array([[p["pixel_x"], p["pixel_y"]] for p in self.georef_points], dtype=np.float32)
            dst_points = np.array([[p["longitude"], p["latitude"]] for p in self.georef_points], dtype=np.float32)

            # If we have exactly 4 points, use more precise perspective transformation
            if len(self.georef_points) == 4:
                # Sort points to ensure consistent order (TL, TR, BR, BL)
                # This is critical for accurate transformation
                # First, compute centroid
                centroid_x = np.mean(src_points[:, 0])
                centroid_y = np.mean(src_points[:, 1])

                # Sort points by quadrant relative to centroid
                sorted_indices = []
                for i, (px, py) in enumerate(src_points):
                    # Determine quadrant (TL, TR, BR, BL)
                    quad = 0
                    if px < centroid_x and py < centroid_y:  # Top-left
                        quad = 0
                    elif px >= centroid_x and py < centroid_y:  # Top-right
                        quad = 1
                    elif px >= centroid_x and py >= centroid_y:  # Bottom-right
                        quad = 2
                    elif px < centroid_x and py >= centroid_y:  # Bottom-left
                        quad = 3
                    sorted_indices.append((quad, i))

                sorted_indices.sort()
                sorted_src = np.array([src_points[idx] for _, idx in sorted_indices], dtype=np.float32)
                sorted_dst = np.array([dst_points[idx] for _, idx in sorted_indices], dtype=np.float32)

                # Calculate perspective transformation matrix with sorted points
                M = cv2.getPerspectiveTransform(sorted_src, sorted_dst)

                # Apply the transformation to get the geographic coordinates
                transformed_point = cv2.perspectiveTransform(np.array([[[x, y]]], dtype=np.float32), M)
                lon, lat = transformed_point[0][0]

                return lat, lon

            # If we have more than 4 points, use a more general interpolation method
            else:
                # Use griddata for interpolation (requires scipy)
                lon = griddata(src_points, [p["longitude"] for p in self.georef_points], np.array([[x, y]]), method='linear')[0]
                lat = griddata(src_points, [p["latitude"] for p in self.georef_points], np.array([[x, y]]), method='linear')[0]

                return lat, lon

        except Exception as e:
            print(f"Failed to transform coordinates: {str(e)}")
            return None, None

    def start_color_sampling(self):
        """Start color sampling mode for excluding colors."""
        if self.cv_image is None:
            messagebox.showwarning("Warning", "Please load an image first.")
            return

        self.sampling_mode = True
        self.status_var.set("Click on the image to sample colors to EXCLUDE. Press ESC to exit sampling mode.")

        # Change cursor to indicate sampling mode
        self.original_canvas.config(cursor="crosshair")

        # Show the color preview box (it will be positioned in on_canvas_move)
        self.color_preview.place(x=0, y=0)

        # Bind Escape key to exit sampling mode
        self.root.bind("<Escape>", self.stop_color_sampling)

    def stop_color_sampling(self, event=None):
        """Stop color sampling mode."""
        self.sampling_mode = False
        self.status_var.set("Color sampling mode ended.")

        # Reset cursor
        self.original_canvas.config(cursor="")

        # Hide the color preview box
        self.color_preview.place_forget()

        # Unbind Escape key
        self.root.unbind("<Escape>")

    def on_canvas_click(self, event):
        """Handle canvas click events for color sampling."""
        if not self.sampling_mode or self.cv_image is None:
            return

        # Get canvas coordinates
        canvas_x = self.original_canvas.canvasx(event.x)
        canvas_y = self.original_canvas.canvasy(event.y)

        # Convert to original image coordinates
        original_x = int(canvas_x / self.scale_factor)
        original_y = int(canvas_y / self.scale_factor)

        # Check if coordinates are within image bounds
        if 0 <= original_x < self.cv_image.shape[1] and 0 <= original_y < self.cv_image.shape[0]:
            # Get RGB color at the clicked point
            rgb_color = self.cv_image[original_y, original_x]

            # Convert to HSV
            hsv_color = cv2.cvtColor(np.uint8([[rgb_color]]), cv2.COLOR_RGB2HSV)[0][0]

            # Add to excluded samples
            self.add_excluded_color_sample(hsv_color)

            # Update preview
            self.update_preview_with_samples()

    def add_excluded_color_sample(self, hsv_color):
        """Add a color sample to the list of excluded color samples."""
        self.excluded_color_samples.append(hsv_color)

        # Update the samples display
        self.update_samples_display()

        # Update the preview
        self.update_preview_with_samples()

    def update_samples_display(self):
        """Update the display of excluded color samples."""
        # Clear the current samples display
        for widget in self.samples_inner_frame.winfo_children():
            widget.destroy()

        # Clear the listbox
        self.samples_listbox.delete(0, tk.END)

        # Add each sample to the display
        for i, hsv_color in enumerate(self.excluded_color_samples):
            # Convert HSV to RGB for display
            rgb_color = cv2.cvtColor(np.uint8([[[hsv_color[0], hsv_color[1], hsv_color[2]]]]), cv2.COLOR_HSV2RGB)[0][0]

            # Create a frame for this sample
            sample_frame = ttk.Frame(self.samples_inner_frame)
            sample_frame.pack(fill=tk.X, padx=5, pady=2)

            # Create a color swatch
            swatch = tk.Canvas(sample_frame, width=20, height=20, bg=f"#{rgb_color[0]:02x}{rgb_color[1]:02x}{rgb_color[2]:02x}")
            swatch.pack(side=tk.LEFT, padx=5)

            # Create a label with the HSV values
            label = ttk.Label(sample_frame, text=f"H:{hsv_color[0]}, S:{hsv_color[1]}, V:{hsv_color[2]}")
            label.pack(side=tk.LEFT, fill=tk.X, expand=True)

            # Add to listbox for selection management
            self.samples_listbox.insert(tk.END, i)

            # Bind click event to select this sample
            sample_frame.bind("<Button-1>", lambda e, idx=i: self.select_sample(idx))
            swatch.bind("<Button-1>", lambda e, idx=i: self.select_sample(idx))
            label.bind("<Button-1>", lambda e, idx=i: self.select_sample(idx))

    def select_sample(self, index):
        """Select a color sample."""
        self.samples_listbox.selection_clear(0, tk.END)
        self.samples_listbox.selection_set(index)

    def remove_selected_sample(self):
        """Remove the selected color sample."""
        selected_indices = self.samples_listbox.curselection()
        if selected_indices:
            index = selected_indices[0]
            self.excluded_color_samples.pop(index)
            self.update_samples_display()
            self.update_preview_with_samples()

    def clear_all_samples(self):
        """Clear all color samples."""
        self.excluded_color_samples = []
        self.update_samples_display()
        self.update_preview_with_samples()

    def update_preview_with_samples(self):
        """Update the preview with the excluded color samples."""
        if self.cv_image is None or not self.excluded_color_samples:
            return

        try:
            # Convert to HSV
            hsv_image = cv2.cvtColor(self.cv_image, cv2.COLOR_RGB2HSV)

            # Create an empty mask for excluded colors
            height, width = self.cv_image.shape[:2]
            excluded_mask = np.zeros((height, width), dtype=np.uint8)

            # Get tolerance value
            tolerance = self.sample_tolerance.get()

            # Apply each excluded color sample to the mask
            for hsv_color in self.excluded_color_samples:
                h, s, v = hsv_color

                # Create range for this color
                h_min = max(0, h - tolerance)
                h_max = min(179, h + tolerance)
                s_min = max(0, s - tolerance)
                s_max = min(255, s + tolerance)
                v_min = max(0, v - tolerance)
                v_max = min(255, v + tolerance)

                # Check if the hue wraps around
                if h_min <= h_max:
                    # Normal case
                    lower_bound = np.array([h_min, s_min, v_min])
                    upper_bound = np.array([h_max, s_max, v_max])
                    temp_mask = cv2.inRange(hsv_image, lower_bound, upper_bound)
                else:
                    # Hue wraps around (e.g., red)
                    lower_bound1 = np.array([0, s_min, v_min])
                    upper_bound1 = np.array([h_max, s_max, v_max])
                    lower_bound2 = np.array([h_min, s_min, v_min])
                    upper_bound2 = np.array([179, s_max, v_max])

                    temp_mask1 = cv2.inRange(hsv_image, lower_bound1, upper_bound1)
                    temp_mask2 = cv2.inRange(hsv_image, lower_bound2, upper_bound2)
                    temp_mask = cv2.bitwise_or(temp_mask1, temp_mask2)

                # Combine with the excluded mask
                excluded_mask = cv2.bitwise_or(excluded_mask, temp_mask)

            # Store the excluded mask
            self.excluded_mask = excluded_mask

            # Create the inverse mask (non-excluded areas)
            self.mask = cv2.bitwise_not(excluded_mask)

            # Clean up the masks with morphological operations
            kernel = np.ones((5, 5), np.uint8)
            self.mask = cv2.morphologyEx(self.mask, cv2.MORPH_OPEN, kernel)
            self.mask = cv2.morphologyEx(self.mask, cv2.MORPH_CLOSE, kernel)

            self.excluded_mask = cv2.morphologyEx(self.excluded_mask, cv2.MORPH_OPEN, kernel)
            self.excluded_mask = cv2.morphologyEx(self.excluded_mask, cv2.MORPH_CLOSE, kernel)

            # Create binary masks (0 or 255)
            _, self.binary_mask = cv2.threshold(self.mask, 127, 255, cv2.THRESH_BINARY)
            _, self.binary_excluded_mask = cv2.threshold(self.excluded_mask, 127, 255, cv2.THRESH_BINARY)

            # Find contours from the binary mask
            contours, _ = cv2.findContours(self.binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Store raw contours for export
            self.raw_contours = contours

            # Filter and simplify for display
            self.polygons = []
            min_area = self.min_area.get()
            epsilon_factor = self.epsilon_factor.get()

            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= min_area:
                    epsilon = epsilon_factor * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    self.polygons.append(approx)

            # Update the display
            self.update_display()

            # Update status
            self.status_var.set(f"Preview updated with {len(self.excluded_color_samples)} excluded color samples: {len(self.polygons)} features found.")

        except Exception as e:
            self.status_var.set(f"Error updating preview: {str(e)}")
            print(f"Preview error: {str(e)}")

    def update_preview(self):
        """Update the preview with the current settings."""
        if self.cv_image is None:
            messagebox.showwarning("Warning", "Please load an image first.")
            return

        # If we have excluded color samples, use those
        if self.excluded_color_samples:
            self.update_preview_with_samples()
            return

        # Otherwise, use the HSV range sliders (not implemented in this version)
        messagebox.showinfo("Info", "Please select colors to exclude by clicking on the image in sampling mode.")

    def update_preview_if_samples(self):
        """Update the preview if there are excluded color samples."""
        if self.excluded_color_samples:
            self.update_preview_with_samples()

    def update_display(self):
        """Update the display with the processed masks and contours."""
        if self.cv_image is None:
            return

        try:
            # Original image already displayed during loading

            # Display the mask (non-excluded areas)
            if hasattr(self, 'mask') and self.mask is not None:
                try:
                    # Ensure mask is valid
                    if not isinstance(self.mask, np.ndarray):
                        raise ValueError("Invalid mask type")

                    # Convert to proper format
                    mask_array = self.mask.copy()
                    if mask_array.dtype != np.uint8:
                        mask_array = mask_array.astype(np.uint8)

                    # Convert single-channel to RGB
                    if len(mask_array.shape) == 2:
                        mask_rgb = cv2.cvtColor(mask_array, cv2.COLOR_GRAY2RGB)
                    else:
                        mask_rgb = mask_array

                    # Create and display mask image
                    mask_image = Image.fromarray(mask_rgb)
                    self.display_image(mask_image, self.mask_canvas)
                except Exception as e:
                    self.status_var.set(f"Mask display error: {str(e)}")
                    print(f"Mask error: {str(e)}")

            # Display the excluded mask
            if hasattr(self, 'excluded_mask') and self.excluded_mask is not None:
                try:
                    # Ensure mask is valid
                    if not isinstance(self.excluded_mask, np.ndarray):
                        raise ValueError("Invalid excluded mask type")

                    # Convert to proper format
                    mask_array = self.excluded_mask.copy()
                    if mask_array.dtype != np.uint8:
                        mask_array = mask_array.astype(np.uint8)

                    # Convert single-channel to RGB
                    if len(mask_array.shape) == 2:
                        mask_rgb = cv2.cvtColor(mask_array, cv2.COLOR_GRAY2RGB)
                    else:
                        mask_rgb = mask_array

                    # Create and display excluded mask image
                    mask_image = Image.fromarray(mask_rgb)
                    self.display_image(mask_image, self.excluded_canvas)
                except Exception as e:
                    self.status_var.set(f"Excluded mask display error: {str(e)}")
                    print(f"Excluded mask error: {str(e)}")

            # Draw contours if available
            if self.polygons and len(self.polygons) > 0:
                try:
                    # Create a copy for drawing
                    contour_img = self.cv_image.copy()

                    # Draw polygons
                    for polygon in self.polygons:
                        polygon_reshaped = polygon.reshape((-1, 2))
                        cv2.polylines(contour_img, [polygon_reshaped.astype(np.int32)], True, (0, 255, 0), 2)

                    # Create and display contour image
                    contour_image = Image.fromarray(contour_img)
                    self.display_image(contour_image, self.contour_canvas)
                except Exception as e:
                    self.status_var.set(f"Contour display error: {str(e)}")
                    print(f"Contour error: {str(e)}")

            # Update status with results
            feature_count = len(self.polygons) if self.polygons else 0
            self.status_var.set(f"Found {feature_count} features. Adjust parameters or export.")

        except Exception as e:
            self.status_var.set(f"Display update error: {str(e)}")
            print(f"Update display error: {str(e)}")

    def export_to_shapefile(self):
        """Export detected polygons to a shapefile with proper georeferencing."""
        if not hasattr(self, 'binary_mask') or self.binary_mask is None:
            messagebox.showwarning("Warning", "No mask to export. Please update the preview first.")
            return

        if not self.georef_points:
            messagebox.showwarning("Warning", "No georeferencing data loaded. The shapefile will not be georeferenced.")
            return

        try:
            # Show progress
            self.status_var.set("Processing polygons for export...")
            self.root.update_idletasks()

            # Use the binary mask directly for highest consistency with what's shown
            # Generate contours with minimal simplification to preserve detail
            contours, _ = cv2.findContours(self.binary_mask.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Minimal simplification for export to preserve detail
            export_polygons = []
            min_area = self.min_area.get()
            # Use much lower epsilon factor for export to preserve detail
            epsilon_factor = min(self.epsilon_factor.get() / 20, 0.00005)

            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= min_area:
                    # Use much less simplification for export
                    epsilon = epsilon_factor * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    export_polygons.append(approx)

            total_polygons = len(export_polygons)
            self.status_var.set(f"Processing {total_polygons} features...")
            self.root.update_idletasks()

            # Process each polygon
            geo_polygons = []
            data = {
                "id": [],
                "area_px": [],
                "centroid_lat": [],
                "centroid_lon": []
            }

            success_count = 0
            error_count = 0

            for i, polygon in enumerate(export_polygons):
                try:
                    # Update progress periodically
                    if i % 10 == 0 or i == total_polygons - 1:
                        self.status_var.set(f"Processing polygon {i+1}/{total_polygons}...")
                        self.root.update_idletasks()

                    # Get pixel coordinates
                    polygon_reshaped = polygon.reshape((-1, 2))

                    # Skip very small polygons
                    if len(polygon_reshaped) < 3:
                        continue

                    # Convert to geographic coordinates
                    geo_coords = []
                    for px, py in polygon_reshaped:
                        lat, lon = self.pixel_to_geo(px, py)
                        if lat is not None and lon is not None:
                            geo_coords.append((lon, lat))  # GeoDataFrame expects (lon, lat) order

                    # Create polygon if we have at least 3 points
                    if len(geo_coords) >= 3:
                        try:
                            geo_poly = Polygon(geo_coords)
                            if geo_poly.is_valid:
                                # Calculate centroid
                                centroid = geo_poly.centroid

                                geo_polygons.append(geo_poly)
                                data["id"].append(i + 1)
                                data["area_px"].append(cv2.contourArea(polygon))
                                data["centroid_lat"].append(centroid.y)
                                data["centroid_lon"].append(centroid.x)
                                success_count += 1
                            else:
                                error_count += 1
                        except Exception as polygon_error:
                            error_count += 1
                            print(f"Invalid polygon {i}: {str(polygon_error)}")
                except Exception as e:
                    error_count += 1
                    print(f"Error processing polygon {i}: {str(e)}")

            if not geo_polygons:
                messagebox.showwarning("Warning", "Failed to create valid geographic polygons.")
                return

            # Create the GeoDataFrame with proper CRS
            self.status_var.set("Creating shapefile...")
            self.root.update_idletasks()

            gdf = gpd.GeoDataFrame(data, geometry=geo_polygons, crs="EPSG:4326")

            # Calculate area in hectares for each polygon
            gdf["area_ha"] = gdf.geometry.to_crs({"proj": "cea"}).area / 10000

            # Save to file
            file_path = filedialog.asksaveasfilename(
                title="Save Shapefile",
                defaultextension=".shp",
                filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
                initialfile="non_excluded_features.shp"
            )

            if file_path:
                self.status_var.set(f"Saving {len(geo_polygons)} polygons to {file_path}...")
                self.root.update_idletasks()

                gdf.to_file(file_path)

                # Create a summary message
                summary = f"Successfully exported {success_count} polygons"
                if error_count > 0:
                    summary += f" ({error_count} failed)"
                summary += f" to {os.path.basename(file_path)}"

                # Add area information
                total_area_ha = gdf["area_ha"].sum()
                if total_area_ha > 0:
                    summary += f"\nTotal area: {total_area_ha:.2f} hectares"

                self.status_var.set(summary)
                messagebox.showinfo("Export Complete", summary)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export shapefile: {str(e)}")
            self.status_var.set(f"Error exporting shapefile: {str(e)}")
            print(f"Export error: {str(e)}")

# Main function to run the application
if __name__ == "__main__":
    root = tk.Tk()
    app = InverseColorExtractorApp(root)
    root.mainloop()
