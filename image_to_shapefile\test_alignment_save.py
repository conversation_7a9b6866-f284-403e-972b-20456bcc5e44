import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import geopandas as gpd
from PIL import Image
import rasterio
from rasterio.transform import from_bounds
from shapely.ops import transform

def test_alignment_and_save(shapefile_path, image_path, config_path, output_path):
    """
    Test the alignment between a shapefile and an image, and save the result to a file.

    Args:
        shapefile_path: Path to the shapefile
        image_path: Path to the image
        config_path: Path to the georeferencing configuration file
        output_path: Path to save the result
    """
    print(f"Loading shapefile: {shapefile_path}")
    gdf = gpd.read_file(shapefile_path)

    print(f"Loading image: {image_path}")
    image = Image.open(image_path)
    image_array = np.array(image)

    print(f"Loading georef config: {config_path}")
    import json
    with open(config_path, 'r') as f:
        georef_config = json.load(f)

    # Get image dimensions
    img_width = image.width
    img_height = image.height

    # Store control points
    control_points = []
    for point in georef_config["points"]:
        control_points.append({
            "pixel_x": point["pixel_x"],
            "pixel_y": point["pixel_y"],
            "longitude": point["longitude"],
            "latitude": point["latitude"]
        })

    # Calculate transformation
    # Calculate average scales
    scales_x = []
    scales_y = []

    for i in range(len(control_points)):
        for j in range(i+1, len(control_points)):
            p1 = control_points[i]
            p2 = control_points[j]

            dx_pixels = abs(p1["pixel_x"] - p2["pixel_x"])
            dy_pixels = abs(p1["pixel_y"] - p2["pixel_y"])

            dx_geo = abs(p1["longitude"] - p2["longitude"])
            dy_geo = abs(p1["latitude"] - p2["latitude"])

            if dx_pixels > 0:
                scales_x.append(dx_geo / dx_pixels)
            if dy_pixels > 0:
                scales_y.append(dy_geo / dy_pixels)

    scale_x = np.median(scales_x) if scales_x else 0.0001
    scale_y = np.median(scales_y) if scales_y else 0.0001

    # Use the first point as reference
    ref_point = control_points[0]
    ref_pixel_x = ref_point["pixel_x"]
    ref_pixel_y = ref_point["pixel_y"]
    ref_lon = ref_point["longitude"]
    ref_lat = ref_point["latitude"]

    # For a more accurate transformation, let's use the 4 control points directly
    # This assumes we have exactly 4 control points in a grid pattern
    if len(control_points) == 4:
        # Sort points by y then x to get them in order
        sorted_points = sorted(control_points, key=lambda p: (p["pixel_y"], p["pixel_x"]))

        # Check if we have a grid pattern (approximately)
        if (abs(sorted_points[0]["pixel_y"] - sorted_points[1]["pixel_y"]) < 10 and
            abs(sorted_points[2]["pixel_y"] - sorted_points[3]["pixel_y"]) < 10 and
            abs(sorted_points[0]["pixel_x"] - sorted_points[2]["pixel_x"]) < 10 and
            abs(sorted_points[1]["pixel_x"] - sorted_points[3]["pixel_x"]) < 10):

            # We have a grid, use the corners for the transformation
            # Reorder points to: top-left, top-right, bottom-right, bottom-left
            tl = sorted_points[0]
            tr = sorted_points[1]
            bl = sorted_points[2]
            br = sorted_points[3]

            # Calculate bounds directly from the control points
            left = min(tl["longitude"], bl["longitude"])
            right = max(tr["longitude"], br["longitude"])
            top = max(tl["latitude"], tr["latitude"])
            bottom = min(bl["latitude"], br["latitude"])

            print(f"Using grid transformation with bounds: {left}, {bottom}, {right}, {top}")
        else:
            # Not a grid, use the general approach
            left = ref_lon - (ref_pixel_x * scale_x)
            right = left + (img_width * scale_x)
            bottom = ref_lat - (img_height - ref_pixel_y) * scale_y
            top = ref_lat + ref_pixel_y * scale_y
            print(f"Using general transformation with calculated bounds")
    else:
        # Not enough points, use the general approach
        left = ref_lon - (ref_pixel_x * scale_x)
        right = left + (img_width * scale_x)
        bottom = ref_lat - (img_height - ref_pixel_y) * scale_y
        top = ref_lat + ref_pixel_y * scale_y
        print(f"Using general transformation with calculated bounds")

    # Create transform
    transform_matrix = from_bounds(left, bottom, right, top, img_width, img_height)

    print(f"Using transformation with scales: {scale_x}, {scale_y}")
    print(f"Bounds: {left}, {bottom}, {right}, {top}")

    # Check if the shapefile has a CRS
    if gdf.crs is None:
        print("Warning: Shapefile has no CRS. Assuming EPSG:4326 (WGS84).")
        gdf.set_crs(epsg=4326, inplace=True)
    elif gdf.crs.to_epsg() != 4326:
        print(f"Converting shapefile from {gdf.crs} to EPSG:4326")
        gdf = gdf.to_crs(epsg=4326)

    # Convert shapefile to pixel coordinates
    pixel_gdf = gdf.copy()

    # Function to convert geo coordinates to pixel coordinates
    def geo_to_pixel(geom):
        def transformer(x, y):
            # Manual transformation based on control points
            # This is a more direct approach that should fix the orientation issue

            # Calculate the position relative to the bounds
            x_ratio = (x - left) / (right - left)
            y_ratio = (y - bottom) / (top - bottom)

            # Convert to pixel coordinates
            # Note: For y, we invert because image coordinates start at the top
            pixel_x = x_ratio * img_width
            pixel_y = (1 - y_ratio) * img_height

            return pixel_x, pixel_y

        return transform(transformer, geom)

    # Apply the transformation to each geometry
    pixel_gdf.geometry = pixel_gdf.geometry.apply(geo_to_pixel)

    print(f"Shapefile bounds (original): {gdf.total_bounds}")
    print(f"Shapefile bounds (pixel): {pixel_gdf.total_bounds}")
    print(f"Image dimensions: {img_width} x {img_height}")

    # Create figure and plot
    fig, ax = plt.subplots(figsize=(12, 10))

    # Display the image
    ax.imshow(image_array)

    # Plot the shapefile
    try:
        # Plot the shapefile manually to avoid aspect ratio issues
        for idx, row in pixel_gdf.iterrows():
            if row.geometry.geom_type == 'Polygon':
                x, y = row.geometry.exterior.xy
                ax.plot(x, y, color='red', linewidth=2)
            elif row.geometry.geom_type == 'MultiPolygon':
                for geom in row.geometry.geoms:
                    x, y = geom.exterior.xy
                    ax.plot(x, y, color='red', linewidth=2)

        # Add control points for reference
        for i, point in enumerate(control_points):
            ax.plot(point["pixel_x"], point["pixel_y"], 'o', color='blue', markersize=8)
            ax.text(point["pixel_x"] + 10, point["pixel_y"] + 10,
                    f"CP{i+1}: ({point['latitude']:.4f}, {point['longitude']:.4f})",
                    color='white', fontsize=8, backgroundcolor='black')

        # Add title
        ax.set_title("Shapefile Overlay on Image")

        # Create custom legend
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], color='red', lw=2, label='Shapefile Boundary'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='Control Points')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        # Remove axes for cleaner look
        ax.set_axis_off()
    except Exception as e:
        print(f"Error plotting shapefile: {str(e)}")
        import traceback
        traceback.print_exc()

    # Save the figure
    print(f"Saving result to: {output_path}")
    fig.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Result saved to: {output_path}")

if __name__ == "__main__":
    # Define file paths
    shapefile_path = "D:\\Gawean Rebinmas\\Tree Counting Project\\Information System Web Tree Counted\\Assets\\HGU REBINMAS\\shp_rebinmasjaya.shp"
    image_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\D. Peta Sebaran Areal HCV_page-0001.png"
    config_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\map_georef_config_4_titik_HCV1.json"
    output_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\alignment_test_result.png"

    # Check if the files exist
    if not os.path.exists(shapefile_path):
        print(f"Error: Shapefile not found at {shapefile_path}")
        sys.exit(1)

    if not os.path.exists(image_path):
        print(f"Error: Image not found at {image_path}")
        sys.exit(1)

    if not os.path.exists(config_path):
        print(f"Error: Georef config not found at {config_path}")
        sys.exit(1)

    # Run the test
    test_alignment_and_save(shapefile_path, image_path, config_path, output_path)
