# Shapefile-Image Alignment Testing Tools

This set of tools helps you test and verify the alignment between georeferenced images and shapefiles. It's particularly useful for checking if a georeferenced image properly aligns with existing GIS data.

## Tools Included

1. **Alignment Tester** (`alignment_tester.py`) - The main tool for testing alignment between a georeferenced image and a shapefile.
2. **Georef Config Creator** (`create_georef_config.py`) - A tool for creating georeferencing configuration files by marking known points.
3. **Test Alignment Script** (`test_alignment.py`) - A simple script to run the alignment test with predefined file paths.

## Requirements

- Python 3.8+
- Required packages:
  - numpy
  - matplotlib
  - geopandas
  - rasterio
  - PIL (Pillow)
  - shapely
  - tkinter
  - cv2 (OpenCV)

You can install these packages using pip:

```
pip install numpy matplotlib geopandas rasterio Pillow shapely opencv-python
```

Note: Tkinter is usually included with Python installations.

## How to Use

### 1. Create a Georeferencing Configuration

Before testing alignment, you need a georeferencing configuration file that maps pixel coordinates to geographic coordinates. If you don't have one:

1. Run the Georef Config Creator:
   ```
   python create_georef_config.py
   ```

2. Load your image using the "Browse..." button.

3. (Optional) Load a reference shapefile to help identify known points.

4. Click "Mark Point" and then click on a location in the image where you know the geographic coordinates.

5. Enter the latitude and longitude for that point and click "Add Point".

6. Repeat steps 4-5 for at least one more point (more points will give better results).

7. Click "Save Config" to save the georeferencing configuration to a JSON file.

### 2. Test Alignment

Once you have a georeferencing configuration file, you can test the alignment:

1. Run the Alignment Tester:
   ```
   python alignment_tester.py
   ```

2. Load your shapefile, image, and georeferencing configuration using the "Browse..." buttons.

3. Click "Test Alignment" to see how well the shapefile aligns with the image.

4. You can save the result as an image using the "Save Result" button.

### 3. Using the Test Script

For convenience, you can use the test script with predefined file paths:

```
python test_alignment.py
```

This will automatically load:
- Shapefile: "D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\HGU REBINMAS\shp_rebinmasjaya.shp"
- Image: "D:\Gawean Rebinmas\Geo  Processing GIS Alat\image_to_shapefile\D. Peta Sebaran Areal HCV_page-0001.png"
- It will also look for any georeferencing configuration files in the same directory as the image.

## How It Works

The alignment testing process works as follows:

1. The georeferencing configuration file provides the mapping between pixel coordinates and geographic coordinates.

2. The tool creates a transformation matrix based on this mapping.

3. The shapefile's geographic coordinates are converted to pixel coordinates using this transformation.

4. The shapefile is overlaid on the image to visually check the alignment.

## Tips for Better Results

1. **Use More Control Points**: The more control points you have in your georeferencing configuration, the more accurate the alignment will be.

2. **Distribute Points Evenly**: Try to place control points evenly across the image, especially near the edges.

3. **Use Recognizable Features**: Choose control points at easily identifiable features that appear in both the image and the shapefile.

4. **Check Multiple Areas**: After testing alignment, check different areas of the image to ensure the alignment is consistent throughout.

## Troubleshooting

If the shapefile doesn't align properly with the image:

1. **Check Coordinate Systems**: Make sure the shapefile and the geographic coordinates in your configuration file use the same coordinate system (e.g., both in WGS84).

2. **Verify Control Points**: Double-check the accuracy of your control points. Even small errors can lead to significant misalignment.

3. **Add More Control Points**: If the alignment is good in some areas but poor in others, try adding more control points, especially in the problematic areas.

4. **Check Image Distortion**: If the image has significant distortion, a simple affine transformation might not be sufficient. Consider using more sophisticated georeferencing methods.

## Advanced Usage

The `AlignmentTester` class can also be used programmatically:

```python
from alignment_tester import AlignmentTester

# Create an instance without GUI
tester = AlignmentTester()

# Load data
tester.load_shapefile("path/to/shapefile.shp")
tester.load_image("path/to/image.png")
tester.load_georef_config("path/to/config.json")

# Create transform
tester.create_transform()

# Convert shapefile to pixels
pixel_gdf = tester.convert_shapefile_to_pixels()

# Now you can use pixel_gdf for further analysis
```
