import geopandas as gpd
import os

print("=== Pemrosesan Area Tanam Belitung dan Belitung Timur ===")

# Periksa keberadaan file input
area_tanam_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/Area_Tanam_Outer_Layer_backup.shp"
batas_desa_path = "D:/Gawean Rebinmas/Tree Counting Project/Information System Web Tree Counted/Assets/batas_desa_belitung/batas_desa_belitung.shp"

if not os.path.exists(area_tanam_path):
    print(f"ERROR: File area tanam tidak ditemukan: {area_tanam_path}")
    exit(1)
    
if not os.path.exists(batas_desa_path):
    print(f"ERROR: File batas desa tidak ditemukan: {batas_desa_path}")
    exit(1)

print("✓ File input ditemukan")

# Muat data
print("\n1. Memuat data shapefile...")
area_tanam = gpd.read_file(area_tanam_path)
batas_desa = gpd.read_file(batas_desa_path)

print(f"   - Area tanam: {len(area_tanam)} fitur")
print(f"   - Batas desa: {len(batas_desa)} fitur")

# Tampilkan informasi WADMKK yang tersedia
print(f"\n2. Nilai WADMKK dalam batas desa:")
wadmkk_values = batas_desa['WADMKK'].unique()
for wadmkk in sorted(wadmkk_values):
    count = len(batas_desa[batas_desa['WADMKK'] == wadmkk])
    print(f"   - {wadmkk}: {count} fitur")

# Intersect
print("\n3. Melakukan interseksi area tanam dengan batas desa...")
intersected = gpd.overlay(area_tanam, batas_desa, how='intersection')
print(f"   - Hasil interseksi: {len(intersected)} fitur")

# Tampilkan distribusi WADMKK setelah interseksi
print(f"\n4. Distribusi WADMKK setelah interseksi:")
wadmkk_intersect = intersected['WADMKK'].value_counts()
for wadmkk, count in wadmkk_intersect.items():
    print(f"   - {wadmkk}: {count} fitur")

# Dissolve berdasarkan WADMKK
print("\n5. Menggabungkan fitur berdasarkan WADMKK...")
dissolved = intersected.dissolve(by='WADMKK')
print(f"   - Hasil akhir: {len(dissolved)} fitur (wilayah)")

# Tampilkan informasi hasil akhir
print("\n6. Ringkasan hasil akhir:")
for idx, row in dissolved.iterrows():
    area_ha = row.geometry.area / 10000  # Konversi dari m² ke hektar (asumsi CRS dalam meter)
    print(f"   - {idx}: {area_ha:.2f} hektar")

# Simpan hasil
output_file = "Area_Tanam_Belitung_BelitungTimur.shp"
print(f"\n7. Menyimpan hasil ke: {output_file}")
dissolved.to_file(output_file)

print("\n=== SELESAI ===")
print(f"✓ File output berhasil dibuat: {output_file}")
print(f"✓ Total wilayah: {len(dissolved)}")
print("\nFile ini berisi area tanam yang sudah dipisah berdasarkan wilayah kabupaten:")
for idx in dissolved.index:
    print(f"  - Wilayah: {idx}")