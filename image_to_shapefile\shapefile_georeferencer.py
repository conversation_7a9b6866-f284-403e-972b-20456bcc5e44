import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.patches as mpatches

class ShapefileGeoReferencerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Shapefile Georeferencer")
        self.root.geometry("1400x900")
        
        # Initialize variables
        self.image_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\D. Peta Sebaran Areal HCV_page-0001.png"
        self.shapefile_path = "D:\\Gawean Rebinmas\\Tree Counting Project\\Information System Web Tree Counted\\Assets\\HGU REBINMAS\\shp_rebinmasjaya.shp"
        self.config_file = "map_georef_config.json"
        self.scale_factor = 1.0
        self.shift_x = 0  # Shift in pixels
        self.shift_y = 0
        self.control_points = []  # List of (image_x, image_y, shapefile_x, shapefile_y)
        
        # Create main frame with paned window for resizable panels
        self.paned_window = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create left panel for controls
        self.control_frame = ttk.LabelFrame(self.paned_window, text="Controls")
        self.paned_window.add(self.control_frame, weight=1)
        
        # Create right panel for map display
        self.map_frame = ttk.LabelFrame(self.paned_window, text="Map Overlay")
        self.paned_window.add(self.map_frame, weight=4)
        
        # Set up matplotlib figure for map display
        self.fig = Figure(figsize=(10, 8), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.map_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add control buttons
        self.setup_control_panel()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Load the image and shapefile
        self.load_initial_data()
        
        # Set initial status
        self.status_var.set("Ready. Adjust shapefile position using the controls.")
        
        # Bind mouse events for interaction
        self.canvas.mpl_connect('button_press_event', self.on_click)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        
        # Mode flags
        self.adding_control_point = False
    
    def setup_control_panel(self):
        # Image controls
        img_frame = ttk.LabelFrame(self.control_frame, text="Image")
        img_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(img_frame, text="Load Image", command=self.load_image).pack(fill=tk.X, padx=5, pady=5)
        
        # Shapefile controls
        shp_frame = ttk.LabelFrame(self.control_frame, text="Shapefile")
        shp_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(shp_frame, text="Load Shapefile", command=self.load_shapefile).pack(fill=tk.X, padx=5, pady=5)
        
        # Position adjustment controls
        pos_frame = ttk.LabelFrame(self.control_frame, text="Position Adjustment")
        pos_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # X shift controls
        x_frame = ttk.Frame(pos_frame)
        x_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(x_frame, text="X Shift:").pack(side=tk.LEFT)
        ttk.Button(x_frame, text="◄", command=lambda: self.shift_shapefile(-10, 0)).pack(side=tk.LEFT, padx=2)
        ttk.Button(x_frame, text="►", command=lambda: self.shift_shapefile(10, 0)).pack(side=tk.LEFT, padx=2)
        
        # Y shift controls
        y_frame = ttk.Frame(pos_frame)
        y_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(y_frame, text="Y Shift:").pack(side=tk.LEFT)
        ttk.Button(y_frame, text="▲", command=lambda: self.shift_shapefile(0, -10)).pack(side=tk.LEFT, padx=2)
        ttk.Button(y_frame, text="▼", command=lambda: self.shift_shapefile(0, 10)).pack(side=tk.LEFT, padx=2)
        
        # Fine adjustment
        fine_frame = ttk.Frame(pos_frame)
        fine_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(fine_frame, text="Fine Adjustment:").pack(side=tk.LEFT)
        ttk.Button(fine_frame, text="X-", command=lambda: self.shift_shapefile(-1, 0)).pack(side=tk.LEFT, padx=2)
        ttk.Button(fine_frame, text="X+", command=lambda: self.shift_shapefile(1, 0)).pack(side=tk.LEFT, padx=2)
        ttk.Button(fine_frame, text="Y-", command=lambda: self.shift_shapefile(0, -1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(fine_frame, text="Y+", command=lambda: self.shift_shapefile(0, 1)).pack(side=tk.LEFT, padx=2)
        
        # Current shift display
        self.shift_label_var = tk.StringVar(value="Current Shift: (0, 0)")
        ttk.Label(pos_frame, textvariable=self.shift_label_var).pack(padx=5, pady=5)
        
        # Zoom controls
        zoom_frame = ttk.LabelFrame(self.control_frame, text="Zoom")
        zoom_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(zoom_frame, text="+", command=self.zoom_in).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        ttk.Button(zoom_frame, text="-", command=self.zoom_out).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5, pady=5)
        
        # Control points
        cp_frame = ttk.LabelFrame(self.control_frame, text="Control Points")
        cp_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(cp_frame, text="Add Control Point", command=self.start_add_control_point).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(cp_frame, text="Clear Control Points", command=self.clear_control_points).pack(fill=tk.X, padx=5, pady=5)
        
        # Control points listbox
        self.points_listbox = tk.Listbox(cp_frame, height=5)
        self.points_listbox.pack(fill=tk.X, padx=5, pady=5)
        
        # GeoReferencing configuration
        config_frame = ttk.LabelFrame(self.control_frame, text="GeoReferencing")
        config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(config_frame, text="Save Configuration", command=self.save_configuration).pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(config_frame, text="Load Configuration", command=self.load_configuration).pack(fill=tk.X, padx=5, pady=5)
        
        # Reset button
        ttk.Button(self.control_frame, text="Reset All", command=self.reset_all).pack(fill=tk.X, padx=5, pady=5)
    
    def load_initial_data(self):
        # Load image if exists
        if os.path.exists(self.image_path):
            self.load_image_from_path(self.image_path)
        else:
            self.status_var.set(f"Image not found: {self.image_path}")
        
        # Load shapefile if exists
        if os.path.exists(self.shapefile_path):
            self.load_shapefile_from_path(self.shapefile_path)
        else:
            self.status_var.set(f"Shapefile not found: {self.shapefile_path}")
    
    def load_image(self):
        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.tif;*.tiff"), ("All files", "*.*")]
        )
        if file_path:
            self.image_path = file_path
            self.load_image_from_path(file_path)
    
    def load_image_from_path(self, path):
        try:
            self.image = plt.imread(path)
            self.update_display()
            self.status_var.set(f"Loaded image: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.status_var.set(f"Error loading image: {str(e)}")
    
    def load_shapefile(self):
        file_path = filedialog.askopenfilename(
            title="Select Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.shapefile_path = file_path
            self.load_shapefile_from_path(file_path)
    
    def load_shapefile_from_path(self, path):
        try:
            self.gdf = gpd.read_file(path)
            self.update_display()
            self.status_var.set(f"Loaded shapefile: {path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load shapefile: {str(e)}")
            self.status_var.set(f"Error loading shapefile: {str(e)}")
    
    def update_display(self):
        self.ax.clear()
        
        # Display image if loaded
        if hasattr(self, 'image'):
            self.ax.imshow(self.image, extent=[0, self.image.shape[1], self.image.shape[0], 0])
        
        # Display shapefile if loaded
        if hasattr(self, 'gdf'):
            # Create a copy of the GeoDataFrame to apply shifts
            shifted_gdf = self.gdf.copy()
            
            # Convert shapefile coordinates to pixel coordinates (simplified for this example)
            # This is where you would implement your coordinate transformation
            # For demonstration, we'll just use a simple scaling and offset
            
            # Get GeoDataFrame bounds
            minx, miny, maxx, maxy = shifted_gdf.total_bounds
            
            # Calculate scale factors
            if hasattr(self, 'image'):
                img_width = self.image.shape[1]
                img_height = self.image.shape[0]
                
                # Scale the shapefile to fit the image
                # This is a simplified approach and would need to be refined
                x_scale = img_width / (maxx - minx) * 0.8
                y_scale = img_height / (maxy - miny) * 0.8
                
                # Center the shapefile
                x_offset = (img_width - (maxx - minx) * x_scale) / 2
                y_offset = (img_height - (maxy - miny) * y_scale) / 2
                
                # Apply the shift
                x_offset += self.shift_x
                y_offset += self.shift_y
                
                # Create a function to transform coordinates
                def transform_coords(geom):
                    from shapely.ops import transform
                    
                    def transformer(x, y):
                        # Transform from geographic to image coordinates
                        new_x = (x - minx) * x_scale + x_offset
                        # Flip y-axis for image coordinates (origin at top-left)
                        new_y = img_height - ((y - miny) * y_scale + y_offset)
                        return new_x, new_y
                    
                    return transform(transformer, geom)
                
                # Apply transformation to geometries
                shifted_gdf.geometry = shifted_gdf.geometry.apply(transform_coords)
                
                # Plot the transformed shapefile
                shifted_gdf.plot(ax=self.ax, facecolor='none', edgecolor='red', linewidth=2)
        
        # Draw control points
        for i, (ix, iy, sx, sy) in enumerate(self.control_points):
            self.ax.plot(ix, iy, 'bo', markersize=8)
            self.ax.plot(sx, sy, 'go', markersize=8)
            self.ax.plot([ix, sx], [iy, sy], 'y-', linewidth=1)
            self.ax.annotate(f"CP{i+1}", (ix, iy), color='blue', fontsize=8)
        
        # Add legend
        if hasattr(self, 'gdf'):
            red_patch = mpatches.Patch(color='red', label='Shapefile')
            blue_dot = mpatches.Patch(color='blue', label='Image Control Point')
            green_dot = mpatches.Patch(color='green', label='Shapefile Control Point')
            self.ax.legend(handles=[red_patch, blue_dot, green_dot], loc='lower right')
        
        self.canvas.draw()
    
    def shift_shapefile(self, dx, dy):
        self.shift_x += dx
        self.shift_y += dy
        self.shift_label_var.set(f"Current Shift: ({self.shift_x}, {self.shift_y})")
        self.update_display()
    
    def zoom_in(self):
        self.ax.set_xlim(self.ax.get_xlim()[0]/1.2, self.ax.get_xlim()[1]/1.2)
        self.ax.set_ylim(self.ax.get_ylim()[0]/1.2, self.ax.get_ylim()[1]/1.2)
        self.canvas.draw()
    
    def zoom_out(self):
        self.ax.set_xlim(self.ax.get_xlim()[0]*1.2, self.ax.get_xlim()[1]*1.2)
        self.ax.set_ylim(self.ax.get_ylim()[0]*1.2, self.ax.get_ylim()[1]*1.2)
        self.canvas.draw()
    
    def start_add_control_point(self):
        self.adding_control_point = True
        self.status_var.set("Click on the image to add a control point, then click on the corresponding location on the shapefile.")
    
    def on_click(self, event):
        if not self.adding_control_point or not event.inaxes:
            return
        
        x, y = event.xdata, event.ydata
        
        if len(self.control_points) == 0 or len(self.control_points[-1]) == 4:
            # Start a new control point
            self.control_points.append([x, y])
            self.status_var.set(f"Image point set at ({x:.1f}, {y:.1f}). Now click on the corresponding shapefile location.")
        else:
            # Complete the current control point
            self.control_points[-1].extend([x, y])
            cp_idx = len(self.control_points)
            img_x, img_y = self.control_points[-1][0], self.control_points[-1][1]
            shp_x, shp_y = x, y
            self.points_listbox.insert(tk.END, f"CP{cp_idx}: ({img_x:.1f}, {img_y:.1f}) -> ({shp_x:.1f}, {shp_y:.1f})")
            self.status_var.set(f"Control point {cp_idx} added.")
            self.adding_control_point = False
            self.update_display()
    
    def on_mouse_move(self, event):
        if event.inaxes:
            x, y = event.xdata, event.ydata
            self.status_var.set(f"Coordinates: ({x:.1f}, {y:.1f})")
    
    def clear_control_points(self):
        if self.control_points and messagebox.askyesno("Confirm", "Are you sure you want to clear all control points?"):
            self.control_points = []
            self.points_listbox.delete(0, tk.END)
            self.update_display()
            self.status_var.set("All control points cleared.")
    
    def save_configuration(self):
        if not hasattr(self, 'image') or not hasattr(self, 'gdf'):
            messagebox.showwarning("Warning", "Both image and shapefile must be loaded.")
            return
        
        try:
            # Create data structure
            data = {
                "image_path": self.image_path,
                "shapefile_path": self.shapefile_path,
                "image_width": self.image.shape[1],
                "image_height": self.image.shape[0],
                "shift_x": self.shift_x,
                "shift_y": self.shift_y,
                "control_points": []
            }
            
            # Add control points if any
            for ix, iy, sx, sy in self.control_points:
                data["control_points"].append({
                    "image_x": ix,
                    "image_y": iy,
                    "shapefile_x": sx,
                    "shapefile_y": sy
                })
            
            # Save to file
            file_path = filedialog.asksaveasfilename(
                title="Save Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialfile="shapefile_georef_config.json"
            )
            
            if file_path:
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=4)
                self.status_var.set(f"Saved configuration to {file_path}")
                messagebox.showinfo("Success", f"Configuration saved to {file_path}")
        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")
            self.status_var.set(f"Error saving configuration: {str(e)}")
    
    def load_configuration(self):
        file_path = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Load paths
            if "image_path" in data and os.path.exists(data["image_path"]):
                self.image_path = data["image_path"]
                self.load_image_from_path(self.image_path)
            
            if "shapefile_path" in data and os.path.exists(data["shapefile_path"]):
                self.shapefile_path = data["shapefile_path"]
                self.load_shapefile_from_path(self.shapefile_path)
            
            # Set shift
            if "shift_x" in data and "shift_y" in data:
                self.shift_x = data["shift_x"]
                self.shift_y = data["shift_y"]
                self.shift_label_var.set(f"Current Shift: ({self.shift_x}, {self.shift_y})")
            
            # Load control points
            self.control_points = []
            self.points_listbox.delete(0, tk.END)
            
            if "control_points" in data:
                for i, cp in enumerate(data["control_points"]):
                    ix = cp["image_x"]
                    iy = cp["image_y"]
                    sx = cp["shapefile_x"]
                    sy = cp["shapefile_y"]
                    self.control_points.append([ix, iy, sx, sy])
                    self.points_listbox.insert(tk.END, f"CP{i+1}: ({ix:.1f}, {iy:.1f}) -> ({sx:.1f}, {sy:.1f})")
            
            self.update_display()
            self.status_var.set(f"Loaded configuration from {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load configuration: {str(e)}")
            self.status_var.set(f"Error loading configuration: {str(e)}")
    
    def reset_all(self):
        if messagebox.askyesno("Confirm", "Are you sure you want to reset all settings?"):
            self.shift_x = 0
            self.shift_y = 0
            self.shift_label_var.set(f"Current Shift: ({self.shift_x}, {self.shift_y})")
            self.control_points = []
            self.points_listbox.delete(0, tk.END)
            self.update_display()
            self.status_var.set("All settings reset.")

def main():
    root = tk.Tk()
    app = ShapefileGeoReferencerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 