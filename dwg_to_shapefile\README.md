# DWG to Shapefile Converter

Program untuk mengkonversi file DWG/DXF ke format Shapefile dengan dukungan berbagai jenis geometri.

## Fitur

- **Konversi Multi-Format**: Mendukung file DWG dan DXF
- **Berbagai Jenis Geometri**: Polygon, Polyline, Line, Point, Circle
- **Atribut Lengkap**: Layer, warna, area, perimeter, panjang
- **GUI Interface**: Antarmuka grafis yang mudah digunakan
- **Command Line**: Dukungan untuk otomatisasi dan batch processing
- **Sistem Koordinat**: Dukungan berbagai CRS (EPSG:32648, 4326, dll)

## Instalasi

1. Pastikan Python 3.8+ terinstall
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Cara Penggunaan

### 1. GUI Mode (Recommended)
Jalankan file `launch_gui.bat` atau:
```bash
python main.py --gui
```

### 2. Command Line Mode
```bash
# Konversi file DWG
python main.py "path/to/file.dwg"

# Dengan opsi output directory dan CRS
python main.py "path/to/file.dwg" -o "output/directory" -c "EPSG:4326"
```

### 3. Quick Test
Jalankan `launch_converter.bat` atau langsung:
```bash
python main.py
```

## Parameter

- `-o, --output`: Directory output (default: sama dengan file input)
- `-c, --crs`: Coordinate Reference System (default: EPSG:32648)
- `--gui`: Launch GUI interface

## Supported CRS

- **EPSG:32648**: UTM Zone 48N (Indonesia Timur)
- **EPSG:32647**: UTM Zone 47N (Indonesia Barat)
- **EPSG:32649**: UTM Zone 49N (Indonesia Timur)
- **EPSG:4326**: WGS84 Geographic
- **EPSG:3857**: Web Mercator

## Output

Program akan membuat shapefile terpisah untuk setiap jenis geometri:
- `filename_polygons.shp`: Polygon tertutup
- `filename_polylines.shp`: Polyline terbuka
- `filename_lines.shp`: Garis tunggal
- `filename_points.shp`: Titik
- `filename_circles.shp`: Lingkaran (sebagai polygon)

## Troubleshooting

### File DWG tidak bisa dibaca
- Pastikan file DWG valid dan tidak corrupt
- Coba konversi manual ke DXF menggunakan AutoCAD/QGIS
- Letakkan file DXF di folder yang sama dengan DWG

### Error saat install dependencies
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

### GUI tidak muncul
- Pastikan tkinter terinstall: `python -m tkinter`
- Gunakan mode command line sebagai alternatif

## Log File

Program akan membuat file log `dwg_conversion.log` yang berisi detail proses konversi.

## Contoh Penggunaan

```python
from main import DWGToShapefileConverter

converter = DWGToShapefileConverter()
created_files = converter.convert_dwg_to_shapefile(
    "HGU 8 UTM.DWG",
    output_dir="output",
    crs="EPSG:32648"
)

print(f"Created {len(created_files)} shapefiles")
```

## Requirements

- Python 3.8+
- ezdxf >= 1.0.0
- geopandas >= 0.13.0
- shapely >= 2.0.0
- pandas >= 1.5.0
- fiona >= 1.8.0
- pyproj >= 3.4.0
