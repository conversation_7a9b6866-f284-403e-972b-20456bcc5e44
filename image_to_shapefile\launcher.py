#!/usr/bin/env python
import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess

class LauncherApp:
    def __init__(self, root):
        self.root = root
        self.root.title("GIS Tools Launcher")
        self.root.geometry("500x350")
        
        # Main frame
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Image to Shapefile Georeferencing Tools", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_text = "Select a tool to launch:"
        desc_label = ttk.Label(main_frame, text=desc_text, wraplength=450)
        desc_label.pack(pady=10)
        
        # Buttons frame
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Tool buttons with descriptions
        self.create_tool_button(
            btn_frame, 
            "Shapefile Georeferencer", 
            "Align images with shapefiles by adjusting position and adding control points",
            lambda: self.run_script("shapefile_georeferencer.py")
        )
        
        self.create_tool_button(
            btn_frame, 
            "Image Feature Extractor", 
            "Extract features from georeferenced images as new shapefiles",
            lambda: self.run_script("image_feature_extractor.py")
        )
        
        self.create_tool_button(
            btn_frame, 
            "Legacy Map Georeferencer", 
            "Basic point-based georeferencing tool (older version)",
            lambda: self.run_script("map_georeferencer.py")
        )
        
        # Footer with information
        footer_frame = ttk.Frame(main_frame)
        footer_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(footer_frame, text="Check README.md for detailed instructions").pack(side=tk.LEFT)
        ttk.Button(footer_frame, text="Exit", command=root.destroy).pack(side=tk.RIGHT)
    
    def create_tool_button(self, parent, title, description, command):
        """Create a button with title and description for a tool"""
        frame = ttk.Frame(parent, relief=tk.GROOVE, borderwidth=2)
        frame.pack(fill=tk.X, pady=5, padx=5, ipady=5)
        
        ttk.Label(frame, text=title, font=("Arial", 11, "bold")).pack(anchor=tk.W, padx=10, pady=(5, 0))
        ttk.Label(frame, text=description, wraplength=400).pack(anchor=tk.W, padx=10, pady=(0, 5))
        ttk.Button(frame, text="Launch", command=command).pack(anchor=tk.E, padx=10)
    
    def run_script(self, script_name):
        """Run the selected Python script"""
        try:
            # Get the directory of the current script
            current_dir = os.path.dirname(os.path.abspath(__file__))
            script_path = os.path.join(current_dir, script_name)
            
            # Check if the script exists
            if not os.path.exists(script_path):
                messagebox.showerror("Error", f"Script not found: {script_path}")
                return
            
            # Get the Python executable that's running this launcher
            python_exe = sys.executable
            
            # Launch the script in a new process
            subprocess.Popen([python_exe, script_path])
            
            # Minimized the launcher
            self.root.iconify()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch {script_name}: {str(e)}")

def main():
    root = tk.Tk()
    app = LauncherApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 