#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Generate Inclave Report

This script generates a printable report from a shapefile containing boundary and inclave data.
The report includes statistics grouped by division and subdivision, including boundary count, 
inclave count, total boundary area, and total inclave area.

Author: AI Assistant
Date: 2023-05-14
"""

import os
import sys
import geopandas as gpd
import pandas as pd
import numpy as np
import logging
import datetime
import matplotlib.pyplot as plt
import webbrowser
from pathlib import Path
import tempfile
import re
from matplotlib.colors import ListedColormap
from io import BytesIO
from reportlab.lib.utils import ImageReader
from reportlab.platypus import Image as ReportLabImage

# Try to import reportlab for PDF generation
try:
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("ReportLab not installed. PDF generation will not be available.")
    print("Install with: pip install reportlab")

# Set up logging
log_filename = f"inclave_report_{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class InclaveReportGenerator:
    """Class to generate reports from boundary and inclave data."""
    
    def __init__(self, input_file=None):
        self.input_file = input_file
        self.gdf = None
        self.boundary_rows = []
        self.inclave_rows = []
        self.summary_data = None
        self.detailed_data = None
        
    def load_shapefile(self, input_file=None):
        """Load the shapefile into a GeoDataFrame."""
        if input_file:
            self.input_file = input_file
            
        if not self.input_file:
            raise ValueError("No input shapefile specified")
            
        logger.info(f"Loading shapefile: {self.input_file}")
        
        try:
            self.gdf = gpd.read_file(self.input_file)
            logger.info(f"Loaded {len(self.gdf)} features")
            
            # Identify boundary and inclave features primarily using HCV_Catego
            self.boundary_rows = []
            self.inclave_rows = []
            
            # Find the HCV field name (it might be HCV_Catego, HCV_Category, or HCV)
            hcv_field = next((field for field in ['HCV_Catego', 'HCV_Category', 'HCV'] 
                            if field in self.gdf.columns), None)
            
            if hcv_field:
                logger.info(f"Using {hcv_field} to identify boundaries and inclaves")
                # Use HCV field for classification
                for idx, row in self.gdf.iterrows():
                    if row[hcv_field] == 0:
                        self.boundary_rows.append(idx)
                    elif row[hcv_field] == 1:
                        self.inclave_rows.append(idx)
            else:
                logger.warning("No HCV field found. Trying to use ID_Feature field.")
                # Fall back to ID_Feature and contained_
                for idx, row in self.gdf.iterrows():
                    if isinstance(row.get('ID_Feature'), str) and ('Boundary-' in row['ID_Feature'] or 'boundary' in row['ID_Feature'].lower()):
                        self.boundary_rows.append(idx)
                    elif isinstance(row.get('ID_Feature'), str) and ('Inclave-' in row['ID_Feature'] or 'inclave' in row['ID_Feature'].lower()):
                        self.inclave_rows.append(idx)
            
            logger.info(f"Found {len(self.boundary_rows)} boundary features and {len(self.inclave_rows)} inclave features")
            
            if not self.boundary_rows or not self.inclave_rows:
                logger.warning("Few or no boundaries/inclaves identified. Results may be incomplete.")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading shapefile: {str(e)}")
            return False
    
    def analyze_data(self):
        """Analyze the data to generate summary statistics."""
        if self.gdf is None:
            logger.error("No data loaded. Call load_shapefile() first.")
            return False
            
        logger.info("Analyzing data to generate summary statistics...")
        
        # Extract the DIVISI field if it exists - it might come from the DIVISI column
        # or be extracted from SUB_DIVISI
        if 'DIVISI' in self.gdf.columns:
            self.gdf['DIVISI_NAME'] = self.gdf['DIVISI']
        elif 'SUB_DIVISI' in self.gdf.columns:
            # Try to extract DIVISI from SUB_DIVISI (assumes format like "SUB DIVISI NAME")
            self.gdf['DIVISI_NAME'] = self.gdf['SUB_DIVISI'].apply(lambda x: 
                                                                 re.sub(r'^SUB\s+DIVISI\s+', '', str(x)) if pd.notna(x) else "UNKNOWN")
        else:
            # If no DIVISI or SUB_DIVISI, create a placeholder
            self.gdf['DIVISI_NAME'] = "UNKNOWN"
            
        # Create boundary and inclave dataframes
        boundary_df = self.gdf.loc[self.boundary_rows].copy()
        inclave_df = self.gdf.loc[self.inclave_rows].copy()
        
        # Ensure required columns exist
        required_fields = ['DIVISI_NAME', 'SUB_DIVISI', 'BLOK', 'luas_aut_1', 'total_incl']
        for field in required_fields:
            if field not in boundary_df.columns:
                boundary_df[field] = None
            if field not in inclave_df.columns and field != 'total_incl':
                inclave_df[field] = None
        
        # Generate summary statistics grouped by DIVISI_NAME and SUB_DIVISI
        summary_data = []
        
        # Group data by DIVISI_NAME
        divisi_groups = boundary_df.groupby('DIVISI_NAME')
        
        for divisi_name, divisi_group in divisi_groups:
            # Get all inclaves for this DIVISI
            divisi_inclaves = inclave_df[inclave_df['DIVISI_NAME'] == divisi_name]
            
            # Calculate DIVISI-level statistics
            divisi_boundary_count = len(divisi_group)
            divisi_inclave_count = len(divisi_inclaves)
            divisi_boundary_area = divisi_group['luas_aut_1'].sum()
            divisi_inclave_area = divisi_inclaves['luas_aut_1'].sum()
            divisi_net_area = divisi_boundary_area - divisi_inclave_area
            
            # Add DIVISI summary row
            summary_data.append({
                'DIVISI': divisi_name,
                'SUB_DIVISI': 'TOTAL',
                'BLOK': '',
                'Boundary_Count': divisi_boundary_count,
                'Inclave_Count': divisi_inclave_count,
                'Total_Boundary_Area': divisi_boundary_area,
                'Total_Inclave_Area': divisi_inclave_area,
                'Net_Area': divisi_net_area,
                'Inclave_Percentage': (divisi_inclave_area / divisi_boundary_area * 100) if divisi_boundary_area > 0 else 0
            })
            
            # Group by SUB_DIVISI within this DIVISI
            sub_divisi_groups = divisi_group.groupby('SUB_DIVISI')
            
            for sub_divisi, sub_group in sub_divisi_groups:
                # Get all inclaves for this SUB_DIVISI
                sub_inclaves = inclave_df[inclave_df['SUB_DIVISI'] == sub_divisi]
                
                # Calculate SUB_DIVISI-level statistics
                sub_boundary_count = len(sub_group)
                sub_inclave_count = len(sub_inclaves)
                sub_boundary_area = sub_group['luas_aut_1'].sum()
                sub_inclave_area = sub_inclaves['luas_aut_1'].sum()
                sub_net_area = sub_boundary_area - sub_inclave_area
                
                # Add SUB_DIVISI summary row
                summary_data.append({
                    'DIVISI': divisi_name,
                    'SUB_DIVISI': sub_divisi,
                    'BLOK': 'TOTAL',
                    'Boundary_Count': sub_boundary_count,
                    'Inclave_Count': sub_inclave_count,
                    'Total_Boundary_Area': sub_boundary_area,
                    'Total_Inclave_Area': sub_inclave_area,
                    'Net_Area': sub_net_area,
                    'Inclave_Percentage': (sub_inclave_area / sub_boundary_area * 100) if sub_boundary_area > 0 else 0
                })
                
                # Group by BLOK within this SUB_DIVISI
                blok_groups = sub_group.groupby('BLOK')
                
                for blok, blok_group in blok_groups:
                    # Get all inclaves for this BLOK
                    blok_inclaves = sub_inclaves[sub_inclaves['BLOK'] == blok]
                    
                    # Calculate BLOK-level statistics
                    blok_boundary_count = len(blok_group)
                    blok_inclave_count = len(blok_inclaves)
                    blok_boundary_area = blok_group['luas_aut_1'].sum()
                    blok_inclave_area = blok_inclaves['luas_aut_1'].sum()
                    blok_net_area = blok_boundary_area - blok_inclave_area
                    
                    # Get inclave IDs
                    if 'contained_' in blok_group.columns:
                        inclave_ids = "; ".join([str(x) for x in blok_group['contained_'].dropna().tolist()])
                    else:
                        inclave_ids = ""
                    
                    # Add BLOK detail row
                    summary_data.append({
                        'DIVISI': divisi_name,
                        'SUB_DIVISI': sub_divisi,
                        'BLOK': blok,
                        'Boundary_Count': blok_boundary_count,
                        'Inclave_Count': blok_inclave_count,
                        'Total_Boundary_Area': blok_boundary_area,
                        'Total_Inclave_Area': blok_inclave_area,
                        'Net_Area': blok_net_area,
                        'Inclave_Percentage': (blok_inclave_area / blok_boundary_area * 100) if blok_boundary_area > 0 else 0,
                        'Inclave_IDs': inclave_ids
                    })
        
        # Create the summary DataFrame
        self.summary_data = pd.DataFrame(summary_data)
        
        # Calculate grand totals
        self.grand_total_boundary_count = len(boundary_df)
        self.grand_total_inclave_count = len(inclave_df)
        self.grand_total_boundary_area = boundary_df['luas_aut_1'].sum()
        self.grand_total_inclave_area = inclave_df['luas_aut_1'].sum()
        self.grand_total_net_area = self.grand_total_boundary_area - self.grand_total_inclave_area
        self.grand_total_inclave_percentage = (self.grand_total_inclave_area / self.grand_total_boundary_area * 100) if self.grand_total_boundary_area > 0 else 0
        
        logger.info("Data analysis complete")
        return True
    
    def generate_html_report(self, output_file=None):
        """Generate an HTML report with the summary statistics."""
        if self.summary_data is None:
            logger.error("No analysis data available. Call analyze_data() first.")
            return False
            
        if output_file is None:
            # Create a default output filename
            if self.input_file:
                basename = os.path.splitext(os.path.basename(self.input_file))[0]
                dirname = os.path.dirname(self.input_file)
                output_file = os.path.join(dirname, f"{basename}_inclave_report.html")
            else:
                timestamp = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')
                output_file = f"inclave_report_{timestamp}.html"
            
        logger.info(f"Generating HTML report: {output_file}")
        
        # Format the summary data for HTML
        # Round numerical values to 2 decimal places
        formatted_summary = self.summary_data.copy()
        numerical_columns = ['Total_Boundary_Area', 'Total_Inclave_Area', 'Net_Area', 'Inclave_Percentage']
        for col in numerical_columns:
            if col in formatted_summary.columns:
                formatted_summary[col] = formatted_summary[col].round(2)
        
        # Generate HTML
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Inclave Analysis Report</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                }}
                h1, h2, h3 {{
                    color: #2c3e50;
                }}
                .summary-box {{
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 20px;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin-bottom: 20px;
                }}
                th, td {{
                    text-align: left;
                    padding: 12px;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #4CAF50;
                    color: white;
                }}
                tr:nth-child(even) {{
                    background-color: #f2f2f2;
                }}
                tr:hover {{
                    background-color: #ddd;
                }}
                .divisi-row {{
                    background-color: #d1e7dd;
                    font-weight: bold;
                }}
                .sub-divisi-row {{
                    background-color: #e2f0d9;
                    font-weight: bold;
                }}
                .footer {{
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #6c757d;
                }}
            </style>
        </head>
        <body>
            <h1>Inclave Analysis Report</h1>
            <p>Report generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary-box">
                <h2>Executive Summary</h2>
                <p><strong>Input file:</strong> {os.path.basename(self.input_file) if self.input_file else "Unknown"}</p>
                <table>
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                        <th>Unit</th>
                    </tr>
                    <tr>
                        <td>Total number of boundaries</td>
                        <td>{self.grand_total_boundary_count}</td>
                        <td>polygons</td>
                    </tr>
                    <tr>
                        <td>Total number of inclaves</td>
                        <td>{self.grand_total_inclave_count}</td>
                        <td>polygons</td>
                    </tr>
                    <tr>
                        <td>Total boundary area</td>
                        <td>{self.grand_total_boundary_area:.2f}</td>
                        <td>hectares</td>
                    </tr>
                    <tr>
                        <td>Total inclave area</td>
                        <td>{self.grand_total_inclave_area:.2f}</td>
                        <td>hectares</td>
                    </tr>
                    <tr>
                        <td>Net area (boundary - inclave)</td>
                        <td>{self.grand_total_net_area:.2f}</td>
                        <td>hectares</td>
                    </tr>
                    <tr>
                        <td>Inclave percentage</td>
                        <td>{self.grand_total_inclave_percentage:.2f}</td>
                        <td>%</td>
                    </tr>
                </table>
            </div>
            
            <h2>Detailed Analysis by Division and Subdivision</h2>
        """
        
        # Add the detailed table with divisions and subdivisions
        html_content += """
            <table>
                <tr>
                    <th>Division</th>
                    <th>Subdivision</th>
                    <th>BLOK</th>
                    <th>Boundaries</th>
                    <th>Inclaves</th>
                    <th>Boundary Area (ha)</th>
                    <th>Inclave Area (ha)</th>
                    <th>Net Area (ha)</th>
                    <th>Inclave %</th>
                </tr>
        """
        
        # Add rows with different styling based on level (DIVISI, SUB_DIVISI, BLOK)
        for _, row in formatted_summary.iterrows():
            row_class = ""
            if row['BLOK'] == '':  # DIVISI level
                row_class = "divisi-row"
            elif row['BLOK'] == 'TOTAL':  # SUB_DIVISI level
                row_class = "sub-divisi-row"
                
            html_content += f"""
                <tr class="{row_class}">
                    <td>{'&nbsp;' if row['BLOK'] != '' else row['DIVISI']}</td>
                    <td>{row['SUB_DIVISI']}</td>
                    <td>{row['BLOK']}</td>
                    <td>{row['Boundary_Count']}</td>
                    <td>{row['Inclave_Count']}</td>
                    <td>{row['Total_Boundary_Area']}</td>
                    <td>{row['Total_Inclave_Area']}</td>
                    <td>{row['Net_Area']}</td>
                    <td>{row['Inclave_Percentage']:.2f}%</td>
                </tr>
            """
        
        html_content += """
            </table>
            
            <div class="footer">
                <p>Generated by Inclave Report Generator</p>
            </div>
        </body>
        </html>
        """
        
        # Write to file
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"HTML report saved to: {output_file}")
            
            # Try to open the report in the default browser
            try:
                webbrowser.open('file://' + os.path.abspath(output_file))
                logger.info("Report opened in default browser")
            except:
                logger.warning("Could not open report in browser automatically")
                
            return True
        except Exception as e:
            logger.error(f"Error saving HTML report: {str(e)}")
            return False
    
    def generate_map_visualization(self, output_file=None):
        """Generate a map visualization showing boundaries and inclaves."""
        if self.gdf is None:
            logger.error("No data loaded. Call load_shapefile() first.")
            return None
        
        logger.info("Generating map visualization...")
        
        # Create boundary and inclave dataframes
        boundary_df = self.gdf.loc[self.boundary_rows].copy()
        inclave_df = self.gdf.loc[self.inclave_rows].copy()
        
        # Create figure and axis
        fig, ax = plt.subplots(figsize=(10, 10))
        
        # Plot boundaries with blue color
        if not boundary_df.empty:
            boundary_df.plot(ax=ax, color='blue', alpha=0.3, edgecolor='black', linewidth=0.5, label='Boundary')
        
        # Plot inclaves with red color
        if not inclave_df.empty:
            inclave_df.plot(ax=ax, color='red', alpha=0.5, edgecolor='black', linewidth=0.5, label='Inclave')
        
        # Group by DIVISI_NAME for different colors
        if 'DIVISI_NAME' in self.gdf.columns:
            unique_divisi = self.gdf['DIVISI_NAME'].dropna().unique()
            
            # Generate a colormap with distinct colors
            from matplotlib import cm
            colormap = cm.get_cmap('tab20', len(unique_divisi))
            
            # Dictionaries to store styled geometries by DIVISI
            styled_boundaries = {}
            styled_inclaves = {}
            
            # Plot boundaries and inclaves by DIVISI with distinct colors
            for i, divisi in enumerate(unique_divisi):
                divisi_boundaries = boundary_df[boundary_df['DIVISI_NAME'] == divisi]
                if not divisi_boundaries.empty:
                    divisi_boundaries.plot(ax=ax, color=colormap(i), alpha=0.3, 
                                         edgecolor='black', linewidth=0.5, label=f'Boundary - {divisi}')
                    styled_boundaries[divisi] = divisi_boundaries
                
                divisi_inclaves = inclave_df[inclave_df['DIVISI_NAME'] == divisi]
                if not divisi_inclaves.empty:
                    divisi_inclaves.plot(ax=ax, color=colormap(i), alpha=0.7, 
                                       edgecolor='black', linewidth=0.5, hatch='///', label=f'Inclave - {divisi}')
                    styled_inclaves[divisi] = divisi_inclaves
        
        # Add labels for boundaries
        for idx, row in boundary_df.iterrows():
            if pd.isna(row.geometry) or row.geometry is None:
                continue
            
            label = None
            if 'BLOK' in row and pd.notna(row['BLOK']):
                label = str(row['BLOK'])
            elif 'ID_Feature' in row and pd.notna(row['ID_Feature']):
                label = str(row['ID_Feature'])
            else:
                continue  # Skip if no label is available
            
            centroid = row.geometry.centroid
            ax.annotate(label, (centroid.x, centroid.y), fontsize=6, 
                        ha='center', va='center', bbox=dict(boxstyle="round,pad=0.2", 
                                                         fc="white", ec="black", alpha=0.7))
        
        # Add a legend
        ax.legend(loc='upper right', fontsize='small')
        
        # Add title and axis labels
        ax.set_title("Spatial Distribution of Boundaries and Inclaves")
        
        # Set aspect equal for proper map display
        ax.set_aspect('equal')
        
        # Remove axis ticks for cleaner map
        ax.set_xticks([])
        ax.set_yticks([])
        
        # Tight layout
        plt.tight_layout()
        
        # Save the figure if an output file is specified
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            logger.info(f"Map visualization saved to: {output_file}")
            return output_file
        else:
            # Save to temporary file
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            plt.savefig(temp_file.name, dpi=300, bbox_inches='tight')
            temp_file.close()
            logger.info(f"Temporary map visualization saved to: {temp_file.name}")
            return temp_file.name
    
    def generate_pdf_report(self, output_file=None):
        """Generate a PDF report with the summary statistics and map visualization."""
        if not REPORTLAB_AVAILABLE:
            logger.error("ReportLab is not installed. Cannot generate PDF.")
            return False
        
        if self.summary_data is None:
            logger.error("No analysis data available. Call analyze_data() first.")
            return False
        
        if output_file is None:
            # Create a default output filename
            if self.input_file:
                basename = os.path.splitext(os.path.basename(self.input_file))[0]
                dirname = os.path.dirname(self.input_file)
                output_file = os.path.join(dirname, f"{basename}_inclave_report.pdf")
            else:
                timestamp = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')
                output_file = f"inclave_report_{timestamp}.pdf"
        
        logger.info(f"Generating PDF report: {output_file}")
        
        try:
            # Format the summary data for PDF
            # Round numerical values to 2 decimal places
            formatted_summary = self.summary_data.copy()
            numerical_columns = ['Total_Boundary_Area', 'Total_Inclave_Area', 'Net_Area', 'Inclave_Percentage']
            for col in numerical_columns:
                if col in formatted_summary.columns:
                    formatted_summary[col] = formatted_summary[col].round(2)
            
            # Generate map visualization
            map_file = self.generate_map_visualization()
            
            # Create a PDF document
            doc = SimpleDocTemplate(output_file, pagesize=landscape(A4))
            story = []
            
            # Get styles
            styles = getSampleStyleSheet()
            title_style = styles['Heading1']
            heading_style = styles['Heading2']
            normal_style = styles['Normal']
            
            # Add title
            story.append(Paragraph("Inclave Analysis Report", title_style))
            story.append(Paragraph(f"Report generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))
            story.append(Spacer(1, 12))
            
            # Add map visualization
            if map_file:
                # Add map title
                story.append(Paragraph("Spatial Distribution of Boundaries and Inclaves", heading_style))
                story.append(Spacer(1, 6))
                
                # Calculate image width to fit in the landscape A4 page
                img_width = 700  # Adjust as needed for your layout
                img = ReportLabImage(map_file, width=img_width, height=img_width * 0.75)
                story.append(img)
                story.append(Spacer(1, 12))
            
            # Add executive summary
            story.append(Paragraph("Executive Summary", heading_style))
            story.append(Paragraph(f"Input file: {os.path.basename(self.input_file) if self.input_file else 'Unknown'}", normal_style))
            story.append(Spacer(1, 6))
            
            # Executive summary table
            summary_data = [
                ['Metric', 'Value', 'Unit'],
                ['Total number of boundaries', str(self.grand_total_boundary_count), 'polygons'],
                ['Total number of inclaves', str(self.grand_total_inclave_count), 'polygons'],
                ['Total boundary area', f"{self.grand_total_boundary_area:.2f}", 'hectares'],
                ['Total inclave area', f"{self.grand_total_inclave_area:.2f}", 'hectares'],
                ['Net area (boundary - inclave)', f"{self.grand_total_net_area:.2f}", 'hectares'],
                ['Inclave percentage', f"{self.grand_total_inclave_percentage:.2f}", '%']
            ]
            
            summary_table = Table(summary_data, colWidths=[200, 100, 100])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.green),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            story.append(summary_table)
            story.append(Spacer(1, 12))
            
            # Add detailed analysis
            story.append(Paragraph("Detailed Analysis by Division and Subdivision", heading_style))
            story.append(Spacer(1, 6))
            
            # Detailed table data
            detailed_data = [['Division', 'Subdivision', 'BLOK', 'Boundaries', 'Inclaves', 
                           'Boundary Area (ha)', 'Inclave Area (ha)', 'Net Area (ha)', 'Inclave %']]
            
            # Add rows with different styling for divisi and sub_divisi levels
            row_styles = []
            row_idx = 1  # Start after header row
            
            for _, row in formatted_summary.iterrows():
                detailed_data.append([
                    '' if row['BLOK'] != '' else row['DIVISI'],
                    row['SUB_DIVISI'],
                    row['BLOK'],
                    str(row['Boundary_Count']),
                    str(row['Inclave_Count']),
                    str(row['Total_Boundary_Area']),
                    str(row['Total_Inclave_Area']),
                    str(row['Net_Area']),
                    f"{row['Inclave_Percentage']:.2f}%"
                ])
                
                # Set styling based on row type
                if row['BLOK'] == '':  # DIVISI level
                    row_styles.append(('BACKGROUND', (0, row_idx), (-1, row_idx), colors.lightgreen))
                    row_styles.append(('FONTNAME', (0, row_idx), (-1, row_idx), 'Helvetica-Bold'))
                elif row['BLOK'] == 'TOTAL':  # SUB_DIVISI level
                    row_styles.append(('BACKGROUND', (0, row_idx), (-1, row_idx), colors.lightgrey))
                    row_styles.append(('FONTNAME', (0, row_idx), (-1, row_idx), 'Helvetica-Bold'))
                
                row_idx += 1
            
            # Create the detailed table
            col_widths = [80, 130, 60, 60, 60, 80, 80, 80, 60]
            detailed_table = Table(detailed_data, colWidths=col_widths)
            
            table_style = [
                ('BACKGROUND', (0, 0), (-1, 0), colors.green),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]
            
            # Add custom row styles
            table_style.extend(row_styles)
            
            detailed_table.setStyle(TableStyle(table_style))
            
            story.append(detailed_table)
            story.append(Spacer(1, 12))
            
            # Build the PDF
            doc.build(story)
            
            # Clean up temporary map file
            if map_file and os.path.exists(map_file) and map_file.startswith(tempfile.gettempdir()):
                try:
                    os.unlink(map_file)
                    logger.info(f"Deleted temporary map file: {map_file}")
                except:
                    pass
            
            logger.info(f"PDF report saved to: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {str(e)}")
            return False
            
def main():
    """Main function to run the inclave report generator."""
    # Check if running as a script with arguments
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        output_html = sys.argv[2] if len(sys.argv) > 2 else None
        output_pdf = os.path.splitext(output_html)[0] + ".pdf" if output_html else None
    else:
        # If no arguments provided, use interactive mode to get the input shapefile
        import tkinter as tk
        from tkinter import filedialog
        
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        input_file = filedialog.askopenfilename(
            title="Select Shapefile for Report",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        
        if not input_file:
            logger.error("No input file selected. Exiting.")
            sys.exit(1)
            
        output_html = filedialog.asksaveasfilename(
            title="Save HTML Report As",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
            defaultextension=".html",
            initialfile=os.path.splitext(os.path.basename(input_file))[0] + "_inclave_report.html"
        )
        
        if not output_html:
            logger.info("No output file selected. Will use default.")
            output_html = None
            
        output_pdf = os.path.splitext(output_html)[0] + ".pdf" if output_html else None
            
    # Create the report generator and process the shapefile
    generator = InclaveReportGenerator(input_file)
    
    if generator.load_shapefile():
        if generator.analyze_data():
            html_saved = generator.generate_html_report(output_html)
            
            if REPORTLAB_AVAILABLE:
                pdf_saved = generator.generate_pdf_report(output_pdf)
                if pdf_saved:
                    logger.info(f"PDF report saved successfully")
                else:
                    logger.warning("PDF report generation failed")
            else:
                logger.warning("ReportLab not available. PDF report was not generated.")
                
            if html_saved:
                logger.info("Report generation completed successfully!")
            else:
                logger.error("Failed to generate HTML report. Check the log for details.")
        else:
            logger.error("Failed to analyze the shapefile data. Check the log for details.")
    else:
        logger.error("Failed to load the shapefile. Check the log for details.")

if __name__ == "__main__":
    main() 