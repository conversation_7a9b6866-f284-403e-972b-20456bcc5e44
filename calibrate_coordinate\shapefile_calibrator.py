import geopandas as gpd
import numpy as np
from shapely.geometry import Point
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.patches import Polygon as MplPolygon
import sys

class ShapefileCalibrator:
    def __init__(self, master):
        self.master = master
        self.master.title("Shapefile Calibrator")
        self.master.geometry("1200x800")
        
        # Initialize variables
        self.reference_shapefile = tk.StringVar()
        self.movable_shapefile = tk.StringVar()
        self.output_shapefile = tk.StringVar()
        self.translate_x = tk.DoubleVar(value=0.0)
        self.translate_y = tk.DoubleVar(value=0.0)
        self.scale_factor = tk.DoubleVar(value=1.0)
        self.rotation_angle = tk.DoubleVar(value=0.0)
        
        # Mouse drag variables
        self.dragging = False
        self.last_x = 0
        self.last_y = 0
        
        # Default paths for convenience
        default_reference = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\HGU REBINMAS\shp_rebinmasjaya.shp"
        default_movable = r"D:\Gawean Rebinmas\Geo  Processing GIS Alat\image_to_shapefile\green_areas.shp"
        
        if os.path.exists(default_reference):
            self.reference_shapefile.set(default_reference)
        
        if os.path.exists(default_movable):
            self.movable_shapefile.set(default_movable)
            # Set default output path
            output_dir = os.path.dirname(default_movable)
            self.output_shapefile.set(os.path.join(output_dir, "calibrated_green_areas.shp"))
        
        # Initialize data
        self.reference_gdf = None
        self.movable_gdf = None
        self.transformed_gdf = None
        
        # Create GUI
        self.create_gui()
        
        # Create matplotlib figure for visualization
        self.create_plot()
        
    def create_gui(self):
        # Create main frame
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel for controls
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding="10")
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # File selection
        file_frame = ttk.LabelFrame(control_frame, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(file_frame, text="Reference Shapefile:").pack(anchor=tk.W)
        ref_entry = ttk.Entry(file_frame, textvariable=self.reference_shapefile, width=40)
        ref_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="Browse...", command=lambda: self.browse_file(self.reference_shapefile)).pack(anchor=tk.W)
        
        ttk.Label(file_frame, text="Shapefile to Calibrate:").pack(anchor=tk.W, pady=(10, 0))
        mov_entry = ttk.Entry(file_frame, textvariable=self.movable_shapefile, width=40)
        mov_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="Browse...", command=lambda: self.browse_file(self.movable_shapefile)).pack(anchor=tk.W)
        
        ttk.Label(file_frame, text="Output Shapefile:").pack(anchor=tk.W, pady=(10, 0))
        out_entry = ttk.Entry(file_frame, textvariable=self.output_shapefile, width=40)
        out_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="Browse...", command=lambda: self.browse_file_save(self.output_shapefile)).pack(anchor=tk.W)
        
        # Transformation controls
        transform_frame = ttk.LabelFrame(control_frame, text="Transformation Parameters", padding="10")
        transform_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Translation X
        ttk.Label(transform_frame, text="X Translation:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(transform_frame, textvariable=self.translate_x, width=10).grid(row=0, column=1, sticky=tk.W, padx=(0, 5))
        ttk.Button(transform_frame, text="-", command=lambda: self.adjust_value(self.translate_x, -10)).grid(row=0, column=2)
        ttk.Button(transform_frame, text="+", command=lambda: self.adjust_value(self.translate_x, 10)).grid(row=0, column=3)
        
        # Translation Y
        ttk.Label(transform_frame, text="Y Translation:").grid(row=1, column=0, sticky=tk.W)
        ttk.Entry(transform_frame, textvariable=self.translate_y, width=10).grid(row=1, column=1, sticky=tk.W, padx=(0, 5))
        ttk.Button(transform_frame, text="-", command=lambda: self.adjust_value(self.translate_y, -10)).grid(row=1, column=2)
        ttk.Button(transform_frame, text="+", command=lambda: self.adjust_value(self.translate_y, 10)).grid(row=1, column=3)
        
        # Scale
        ttk.Label(transform_frame, text="Scale Factor:").grid(row=2, column=0, sticky=tk.W)
        ttk.Entry(transform_frame, textvariable=self.scale_factor, width=10).grid(row=2, column=1, sticky=tk.W, padx=(0, 5))
        ttk.Button(transform_frame, text="-", command=lambda: self.adjust_value(self.scale_factor, -0.01, min_val=0.01)).grid(row=2, column=2)
        ttk.Button(transform_frame, text="+", command=lambda: self.adjust_value(self.scale_factor, 0.01)).grid(row=2, column=3)
        
        # Rotation
        ttk.Label(transform_frame, text="Rotation (degrees):").grid(row=3, column=0, sticky=tk.W)
        ttk.Entry(transform_frame, textvariable=self.rotation_angle, width=10).grid(row=3, column=1, sticky=tk.W, padx=(0, 5))
        ttk.Button(transform_frame, text="-", command=lambda: self.adjust_value(self.rotation_angle, -1)).grid(row=3, column=2)
        ttk.Button(transform_frame, text="+", command=lambda: self.adjust_value(self.rotation_angle, 1)).grid(row=3, column=3)
        
        # Drag mode info
        ttk.Label(transform_frame, text="Mouse Controls:", font=("Arial", 10, "bold")).grid(row=4, column=0, columnspan=4, sticky=tk.W, pady=(10, 0))
        ttk.Label(transform_frame, text="• Left click and drag: Move shapefile").grid(row=5, column=0, columnspan=4, sticky=tk.W)
        ttk.Label(transform_frame, text="• Scroll wheel: Zoom in/out").grid(row=6, column=0, columnspan=4, sticky=tk.W)
        ttk.Label(transform_frame, text="• Right click and drag: Pan view").grid(row=7, column=0, columnspan=4, sticky=tk.W)
        
        # Action buttons
        action_frame = ttk.Frame(control_frame)
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(action_frame, text="Load Shapefiles", command=self.load_shapefiles).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(action_frame, text="Apply Transformation", command=self.apply_transformation).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(action_frame, text="Save Calibrated Shapefile", command=self.save_shapefile).pack(fill=tk.X)
        
        # Zoom controls
        zoom_frame = ttk.LabelFrame(control_frame, text="Zoom Controls", padding="10")
        zoom_frame.pack(fill=tk.X, pady=(0, 10))
        
        zoom_buttons = ttk.Frame(zoom_frame)
        zoom_buttons.pack(fill=tk.X)
        
        ttk.Button(zoom_buttons, text="Zoom In", command=self.zoom_in).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=(0, 2))
        ttk.Button(zoom_buttons, text="Zoom Out", command=self.zoom_out).pack(side=tk.LEFT, expand=True, fill=tk.X, padx=(2, 0))
        ttk.Button(zoom_frame, text="Reset View", command=self.reset_view).pack(fill=tk.X, pady=(5, 0))
        
    def create_plot(self):
        # Right panel for visualization
        plot_frame = ttk.LabelFrame(self.master, text="Visualization", padding="10")
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Create matplotlib figure and canvas
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add toolbar for pan/zoom
        toolbar_frame = ttk.Frame(plot_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # Initialize empty plot
        self.ax.set_title("Shapefile Visualization")
        self.ax.set_xlabel("Longitude")
        self.ax.set_ylabel("Latitude")
        self.ax.grid(True)
        
        # Connect mouse events for dragging
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        
        self.canvas.draw()
    
    def zoom_in(self):
        self.ax.set_xlim(self.ax.get_xlim()[0] * 0.9, self.ax.get_xlim()[1] * 0.9)
        self.ax.set_ylim(self.ax.get_ylim()[0] * 0.9, self.ax.get_ylim()[1] * 0.9)
        self.canvas.draw()
    
    def zoom_out(self):
        self.ax.set_xlim(self.ax.get_xlim()[0] * 1.1, self.ax.get_xlim()[1] * 1.1)
        self.ax.set_ylim(self.ax.get_ylim()[0] * 1.1, self.ax.get_ylim()[1] * 1.1)
        self.canvas.draw()
    
    def reset_view(self):
        if self.reference_gdf is not None or self.transformed_gdf is not None:
            self.ax.autoscale()
            self.canvas.draw()
    
    def on_mouse_press(self, event):
        if event.button == 1 and event.inaxes == self.ax:  # Left-click in plot area
            self.dragging = True
            self.last_x = event.xdata
            self.last_y = event.ydata
            # Change cursor to indicate dragging mode
            self.canvas.get_tk_widget().config(cursor="fleur")  # Hand cursor
    
    def on_mouse_release(self, event):
        if event.button == 1:  # Left-click release
            self.dragging = False
            # Reset cursor
            self.canvas.get_tk_widget().config(cursor="")
    
    def on_mouse_move(self, event):
        if self.dragging and event.inaxes == self.ax and self.reference_gdf is not None and self.movable_gdf is not None:
            # Calculate delta
            dx = event.xdata - self.last_x
            dy = event.ydata - self.last_y
            
            # Update last position
            self.last_x = event.xdata
            self.last_y = event.ydata
            
            # Update translation values
            current_x = self.translate_x.get()
            current_y = self.translate_y.get()
            self.translate_x.set(current_x + dx)
            self.translate_y.set(current_y + dy)
            
            # Apply transformation
            self.apply_transformation()
    
    def browse_file(self, string_var):
        filename = filedialog.askopenfilename(
            title="Select Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            string_var.set(filename)
    
    def browse_file_save(self, string_var):
        filename = filedialog.asksaveasfilename(
            title="Save Shapefile As",
            defaultextension=".shp",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            string_var.set(filename)
    
    def adjust_value(self, var, delta, min_val=None):
        current = var.get()
        new_val = current + delta
        if min_val is not None and new_val < min_val:
            new_val = min_val
        var.set(new_val)
        # Apply transformation if data is loaded
        if self.reference_gdf is not None and self.movable_gdf is not None:
            self.apply_transformation()
    
    def load_shapefiles(self):
        reference_path = self.reference_shapefile.get()
        movable_path = self.movable_shapefile.get()
        
        if not os.path.exists(reference_path):
            messagebox.showerror("Error", f"Reference shapefile not found: {reference_path}")
            return
        
        if not os.path.exists(movable_path):
            messagebox.showerror("Error", f"Movable shapefile not found: {movable_path}")
            return
        
        try:
            self.reference_gdf = gpd.read_file(reference_path)
            self.movable_gdf = gpd.read_file(movable_path)
            
            # Ensure both are in the same CRS
            if self.reference_gdf.crs != self.movable_gdf.crs:
                self.movable_gdf = self.movable_gdf.to_crs(self.reference_gdf.crs)
                messagebox.showinfo("CRS Conversion", 
                                    f"Converted movable shapefile from {self.movable_gdf.crs} to {self.reference_gdf.crs}")
            
            # Create a copy for transformation
            self.transformed_gdf = self.movable_gdf.copy()
            
            # Update plot
            self.update_plot()
            messagebox.showinfo("Success", "Shapefiles loaded successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load shapefiles: {str(e)}")
    
    def apply_transformation(self):
        if self.movable_gdf is None:
            messagebox.showerror("Error", "Please load shapefiles first")
            return
        
        try:
            # Get transformation parameters
            tx = self.translate_x.get()
            ty = self.translate_y.get()
            scale = self.scale_factor.get()
            rotation_deg = self.rotation_angle.get()
            rotation_rad = np.radians(rotation_deg)
            
            # Make a copy of the original geometry
            self.transformed_gdf = self.movable_gdf.copy()
            
            # Calculate center of the geometry for rotation and scaling
            if not self.transformed_gdf.empty:
                bounds = self.transformed_gdf.total_bounds
                center_x = (bounds[0] + bounds[2]) / 2
                center_y = (bounds[1] + bounds[3]) / 2
                
                # Apply transformation to each geometry
                def transform_geometry(geom):
                    # Convert to numpy array for transformation
                    if hasattr(geom, 'exterior'):
                        # For Polygons
                        x, y = geom.exterior.xy
                        coords = np.column_stack((x, y))
                        
                        # Transform holes if any
                        holes = []
                        for interior in geom.interiors:
                            ix, iy = interior.xy
                            hole_coords = np.column_stack((ix, iy))
                            transformed_hole = transform_coordinates(hole_coords, center_x, center_y, tx, ty, scale, rotation_rad)
                            holes.append(transformed_hole)
                    else:
                        # For other geometries (points, lines)
                        coords = np.array(geom.coords)
                
                    # Apply transformation
                    transformed_coords = transform_coordinates(coords, center_x, center_y, tx, ty, scale, rotation_rad)
                    
                    # Create new geometry based on original type
                    if hasattr(geom, 'exterior'):
                        from shapely.geometry import Polygon
                        return Polygon(transformed_coords, holes)
                    elif geom.geom_type == 'LineString':
                        from shapely.geometry import LineString
                        return LineString(transformed_coords)
                    elif geom.geom_type == 'Point':
                        from shapely.geometry import Point
                        return Point(transformed_coords[0])
                    else:
                        # Handle other geometry types if needed
                        return geom
                
                # Apply transformation to each geometry
                self.transformed_gdf['geometry'] = self.transformed_gdf['geometry'].apply(transform_geometry)
                
                # Update plot
                self.update_plot()
                
                # Only show message if not called from mouse dragging
                if not self.dragging:
                    messagebox.showinfo("Success", "Transformation applied")
            else:
                messagebox.showwarning("Warning", "No geometries to transform")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply transformation: {str(e)}")
    
    def save_shapefile(self):
        if self.transformed_gdf is None:
            messagebox.showerror("Error", "Please apply transformation first")
            return
        
        output_path = self.output_shapefile.get()
        if not output_path:
            messagebox.showerror("Error", "Please specify an output file path")
            return
        
        try:
            # Save the transformed shapefile
            self.transformed_gdf.to_file(output_path)
            messagebox.showinfo("Success", f"Calibrated shapefile saved to: {output_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save shapefile: {str(e)}")
    
    def update_plot(self):
        # Clear previous plot
        self.ax.clear()
        
        if self.reference_gdf is not None:
            # Plot reference shapefile in blue
            self.reference_gdf.plot(ax=self.ax, color='blue', alpha=0.5, label='Reference')
        
        if self.movable_gdf is not None:
            # Plot original movable shapefile in red (dashed)
            self.movable_gdf.boundary.plot(ax=self.ax, color='red', linestyle='--', alpha=0.5, label='Original')
        
        if self.transformed_gdf is not None:
            # Plot transformed shapefile in green
            self.transformed_gdf.plot(ax=self.ax, color='green', alpha=0.5, label='Transformed')
        
        # Set plot properties
        self.ax.set_title("Shapefile Visualization")
        self.ax.set_xlabel("Longitude")
        self.ax.set_ylabel("Latitude")
        self.ax.grid(True)
        self.ax.legend()
        
        # Auto-adjust view if this is the first load
        if not self.dragging:
            if self.reference_gdf is not None or self.transformed_gdf is not None:
                self.ax.autoscale()
        
        self.canvas.draw()


def transform_coordinates(coords, center_x, center_y, tx, ty, scale, rotation_rad):
    # Translate to origin
    coords = coords - [center_x, center_y]
    
    # Scale
    coords = coords * scale
    
    # Rotate
    rotation_matrix = np.array([
        [np.cos(rotation_rad), -np.sin(rotation_rad)],
        [np.sin(rotation_rad), np.cos(rotation_rad)]
    ])
    coords = np.dot(coords, rotation_matrix.T)
    
    # Translate back and apply additional translation
    coords = coords + [center_x + tx, center_y + ty]
    
    return coords


if __name__ == "__main__":
    # Create Tkinter root window
    root = tk.Tk()
    
    # Create the application
    app = ShapefileCalibrator(root)
    
    # Start the GUI event loop
    root.mainloop() 