import os
import sys
import tkinter as tk
from alignment_tester import AlignmentTester

def run_test():
    """Run the alignment test with the specified files."""
    # Define file paths
    shapefile_path = "D:\\Gawean Rebinmas\\Tree Counting Project\\Information System Web Tree Counted\\Assets\\HGU REBINMAS\\shp_rebinmasjaya.shp"
    image_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\D. Peta Sebaran Areal HCV_page-0001.png"

    # Check if the files exist
    if not os.path.exists(shapefile_path):
        print(f"Error: Shapefile not found at {shapefile_path}")
        return

    if not os.path.exists(image_path):
        print(f"Error: Image not found at {image_path}")
        return

    # Create the GUI
    root = tk.Tk()
    app = AlignmentTester(root)

    # Set the file paths
    app.shapefile_var.set(shapefile_path)
    app.shapefile_path = shapefile_path

    app.image_var.set(image_path)
    app.image_path = image_path

    # Use the specific georef config file
    config_path = "D:\\Gawean Rebinmas\\Geo  Processing GIS Alat\\image_to_shapefile\\map_georef_config_4_titik_HCV1.json"

    if os.path.exists(config_path):
        app.config_var.set(config_path)
        app.load_georef_config(config_path)
        print(f"Loaded georef config: {config_path}")
    else:
        print(f"Georef config file not found at: {config_path}")
        print("Looking for alternative config files...")

        # Fall back to looking for any georef config files
        image_dir = os.path.dirname(image_path)
        config_files = [f for f in os.listdir(image_dir) if f.endswith('.json') and 'georef' in f.lower()]

        if config_files:
            # Use the first config file found
            alt_config_path = os.path.join(image_dir, config_files[0])
            app.config_var.set(alt_config_path)
            app.load_georef_config(alt_config_path)
            print(f"Found and loaded alternative georef config: {alt_config_path}")
        else:
            print("No georef config files found. You'll need to select one manually.")

    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    run_test()
