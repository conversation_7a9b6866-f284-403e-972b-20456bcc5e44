#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tree Detection Point Shapefile Combiner

This utility combines tree detection point shapefiles from multiple divisions, subdivisions, and blocks
into a single shapefile, while preserving metadata about the organizational structure.

Author: AI Assistant
Date: 2023-07-10
"""

import os
import sys
import geopandas as gpd
import pandas as pd
import argparse
import glob
import re
import logging
from datetime import datetime
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from shapely.geometry import Point
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Set up logging
log_dir = "log_history_development"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"combine_detection_points_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

class DetectionPointCombiner:
    """Class to combine tree detection point shapefiles."""

    def __init__(self, root=None):
        """Initialize the combiner."""
        self.root = root
        self.status_var = None
        self.progress_var = None
        self.division_path_var = None
        self.boundary_path_var = None
        self.output_path_var = None

        if root:
            self.setup_gui()

    def setup_gui(self):
        """Set up the GUI."""
        self.root.title("Tree Detection Point Combiner")
        self.root.geometry("800x600")

        # Create main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create variables
        self.division_path_var = tk.StringVar()
        self.boundary_path_var = tk.StringVar()
        self.output_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value="Ready")
        self.progress_var = tk.DoubleVar(value=0)

        # Create widgets
        ttk.Label(main_frame, text="Division Path:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.division_path_var, width=60).grid(row=0, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="Browse...", command=self.browse_division_path).grid(row=0, column=2, sticky=tk.W, pady=5)

        ttk.Label(main_frame, text="Boundary Shapefile:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.boundary_path_var, width=60).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="Browse...", command=self.browse_boundary_path).grid(row=1, column=2, sticky=tk.W, pady=5)

        ttk.Label(main_frame, text="Output Shapefile:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_path_var, width=60).grid(row=2, column=1, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="Browse...", command=self.browse_output_path).grid(row=2, column=2, sticky=tk.W, pady=5)

        # Process button
        self.process_button = ttk.Button(main_frame, text="Process", command=self.process_gui)
        self.process_button.grid(row=3, column=0, columnspan=3, pady=10)

        # Progress bar
        ttk.Label(main_frame, text="Progress:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=4, column=1, columnspan=2, sticky=tk.W+tk.E, pady=5)

        # Status label
        ttk.Label(main_frame, text="Status:").grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, textvariable=self.status_var).grid(row=5, column=1, columnspan=2, sticky=tk.W, pady=5)

        # Log text
        ttk.Label(main_frame, text="Log:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.log_text = tk.Text(main_frame, height=15, width=80)
        self.log_text.grid(row=7, column=0, columnspan=3, sticky=tk.W+tk.E+tk.N+tk.S, pady=5)

        # Scrollbar for log text
        scrollbar = ttk.Scrollbar(main_frame, command=self.log_text.yview)
        scrollbar.grid(row=7, column=3, sticky=tk.N+tk.S)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # Configure grid
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)

        # Add custom logging handler
        self.log_handler = TextHandler(self.log_text)
        logging.getLogger().addHandler(self.log_handler)

    def browse_division_path(self):
        """Browse for division path."""
        path = filedialog.askdirectory(title="Select Division Directory")
        if path:
            self.division_path_var.set(path)

    def browse_boundary_path(self):
        """Browse for boundary shapefile."""
        path = filedialog.askopenfilename(
            title="Select Boundary Shapefile",
            filetypes=[("Shapefiles", "*.shp"), ("All files", "*.*")]
        )
        if path:
            self.boundary_path_var.set(path)

    def browse_output_path(self):
        """Browse for output shapefile."""
        path = filedialog.asksaveasfilename(
            title="Save Combined Shapefile",
            defaultextension=".shp",
            filetypes=[("Shapefiles", "*.shp"), ("All files", "*.*")]
        )
        if path:
            self.output_path_var.set(path)

    def process_gui(self):
        """Process files from GUI."""
        try:
            self.process_button.config(state=tk.DISABLED)
            self.status_var.set("Processing...")
            self.progress_var.set(0)
            self.root.update()

            division_path = self.division_path_var.get()
            boundary_path = self.boundary_path_var.get()
            output_path = self.output_path_var.get()

            if not division_path or not os.path.isdir(division_path):
                messagebox.showerror("Error", "Please select a valid division directory")
                self.process_button.config(state=tk.NORMAL)
                return

            if not boundary_path or not os.path.isfile(boundary_path):
                messagebox.showerror("Error", "Please select a valid boundary shapefile")
                self.process_button.config(state=tk.NORMAL)
                return

            if not output_path:
                messagebox.showerror("Error", "Please specify an output shapefile path")
                self.process_button.config(state=tk.NORMAL)
                return

            # Process files
            self.combine_detection_points(division_path, boundary_path, output_path)

            self.status_var.set("Processing completed")
            self.progress_var.set(100)
            messagebox.showinfo("Success", f"Combined detection points saved to {output_path}")
        except Exception as e:
            logging.error(f"Error processing files: {str(e)}")
            messagebox.showerror("Error", f"Error processing files: {str(e)}")
        finally:
            self.process_button.config(state=tk.NORMAL)

    def scan_detection_folders(self, division_path):
        """
        Scan for detection point shapefiles.

        Args:
            division_path: Path to division folder

        Returns:
            List of dictionaries with file paths and metadata
        """
        logging.info(f"Scanning for detection point shapefiles in {division_path}")
        detection_files = []

        # Get division name from the folder name
        division_name = os.path.basename(division_path)

        # Walk through the directory structure
        for root, dirs, files in os.walk(division_path):
            # Look for "Hasil Deteksi Point" folders (case-insensitive)
            if "hasil deteksi point" in root.lower():
                # Get subdivision name from parent directory
                subdivision_path = os.path.dirname(root)
                subdivision_name = os.path.basename(subdivision_path)

                logging.info(f"Found detection point folder: {root}")
                logging.info(f"Subdivision: {subdivision_name}")

                # Find all .shp files that don't have "inclave" in their name
                for file in files:
                    if file.endswith('.shp') and 'inclave' not in file.lower():
                        # Extract block name from folder structure or filename
                        block_name = self.extract_block_name(root, file)

                        detection_files.append({
                            'file_path': os.path.join(root, file),
                            'division': division_name,
                            'subdivision': subdivision_name,
                            'block': block_name
                        })

                        logging.info(f"Found detection point file: {file}")
                        logging.info(f"Block: {block_name}")

        logging.info(f"Found {len(detection_files)} detection point files")
        return detection_files

    def extract_block_name(self, folder_path, filename):
        """
        Extract block name from folder path or filename.

        Args:
            folder_path: Path to folder containing the file
            filename: Name of the file

        Returns:
            Block name
        """
        # Try to extract from folder name first
        folder_name = os.path.basename(folder_path)

        # Look for patterns like P_07_04, P0704, etc.
        block_pattern = r'P[_\s]?(\d{2})[_\s/]?(\d{2})'
        match = re.search(block_pattern, folder_name)

        if match:
            # Format as P XX / XX
            return f"P {match.group(1)} / {match.group(2)}"

        # Try to extract from filename
        match = re.search(block_pattern, filename)
        if match:
            return f"P {match.group(1)} / {match.group(2)}"

        # If all else fails, use the folder name
        return folder_name

    def read_boundary_polygons(self, boundary_path):
        """
        Read boundary polygon shapefile.

        Args:
            boundary_path: Path to boundary polygon shapefile

        Returns:
            GeoDataFrame with boundary polygons
        """
        logging.info(f"Reading boundary polygon shapefile: {boundary_path}")

        try:
            boundaries_gdf = gpd.read_file(boundary_path)
            logging.info(f"Read {len(boundaries_gdf)} boundary polygons")

            # Check for required fields
            required_fields = ['DIVISI', 'SUB_DIVISI', 'BLOK']
            missing_fields = [field for field in required_fields if field not in boundaries_gdf.columns]

            if missing_fields:
                logging.warning(f"Missing required fields in boundary shapefile: {missing_fields}")

                # Try to find alternative field names
                for field in missing_fields:
                    alt_fields = [col for col in boundaries_gdf.columns if field.lower() in col.lower()]
                    if alt_fields:
                        logging.info(f"Using {alt_fields[0]} as substitute for {field}")
                        boundaries_gdf[field] = boundaries_gdf[alt_fields[0]]

            return boundaries_gdf
        except Exception as e:
            logging.error(f"Error reading boundary shapefile: {str(e)}")
            raise

    def spatial_join_points_boundaries(self, points_gdf, boundaries_gdf):
        """
        Perform spatial join between points and boundaries.

        Args:
            points_gdf: GeoDataFrame with points
            boundaries_gdf: GeoDataFrame with boundaries

        Returns:
            GeoDataFrame with points enriched with boundary attributes
        """
        logging.info("Performing spatial join between points and boundaries")

        try:
            # Ensure both GDFs have the same CRS
            if points_gdf.crs != boundaries_gdf.crs:
                logging.info(f"Reprojecting points to match boundary CRS: {boundaries_gdf.crs}")
                points_gdf = points_gdf.to_crs(boundaries_gdf.crs)

            # Verify that we're working with point geometries
            if not all(geom.geom_type == 'Point' for geom in points_gdf.geometry):
                logging.warning("Non-point geometries found in points GeoDataFrame")
                # Filter to keep only point geometries
                points_gdf = points_gdf[points_gdf.geometry.geom_type == 'Point']
                logging.info(f"Filtered to {len(points_gdf)} point geometries")

            # Perform spatial join
            joined_gdf = gpd.sjoin(points_gdf, boundaries_gdf, how="left", predicate="within")

            # Ensure we only have point geometries in the result
            if not all(geom.geom_type == 'Point' for geom in joined_gdf.geometry):
                logging.warning("Non-point geometries found in joined GeoDataFrame")
                joined_gdf = joined_gdf[joined_gdf.geometry.geom_type == 'Point']
                logging.info(f"Filtered to {len(joined_gdf)} point geometries")

            logging.info(f"Spatial join completed, {len(joined_gdf)} points processed")
            return joined_gdf
        except Exception as e:
            logging.error(f"Error performing spatial join: {str(e)}")
            raise

    def combine_detection_points(self, division_path, boundary_path, output_path):
        """
        Combine detection point shapefiles.

        Args:
            division_path: Path to division folder
            boundary_path: Path to boundary polygon shapefile
            output_path: Path to save the combined output shapefile
        """
        logging.info("Starting detection point combination process")

        try:
            # Scan for detection point shapefiles
            detection_files = self.scan_detection_folders(division_path)

            if not detection_files:
                logging.warning("No detection point files found")
                return

            # Read boundary polygons
            boundaries_gdf = self.read_boundary_polygons(boundary_path)

            # Process each detection point file
            all_points = []
            total_files = len(detection_files)

            for i, file_info in enumerate(detection_files):
                try:
                    # Update progress
                    progress = (i / total_files) * 100
                    if self.progress_var:
                        self.progress_var.set(progress)
                        self.root.update()

                    logging.info(f"Processing file {i+1}/{total_files}: {file_info['file_path']}")

                    # Read detection point shapefile
                    points_gdf = gpd.read_file(file_info['file_path'])

                    # Add metadata from file_info
                    points_gdf['DIVISION'] = file_info['division']
                    points_gdf['SUBDIVISION'] = file_info['subdivision']
                    points_gdf['BLOCK'] = file_info['block']

                    # Perform spatial join with boundaries
                    points_gdf = self.spatial_join_points_boundaries(points_gdf, boundaries_gdf)

                    # Append to list
                    all_points.append(points_gdf)

                    logging.info(f"Added {len(points_gdf)} points from {os.path.basename(file_info['file_path'])}")
                except Exception as e:
                    logging.error(f"Error processing file {file_info['file_path']}: {str(e)}")

            # Combine all points
            if all_points:
                combined_gdf = pd.concat(all_points, ignore_index=True)

                # Create a proper GeoDataFrame
                combined_gdf = gpd.GeoDataFrame(combined_gdf, geometry='geometry', crs=boundaries_gdf.crs)

                # Ensure we only have point geometries in the result
                if not all(geom.geom_type == 'Point' for geom in combined_gdf.geometry):
                    logging.warning("Non-point geometries found in combined GeoDataFrame")
                    point_count = len(combined_gdf)
                    combined_gdf = combined_gdf[combined_gdf.geometry.geom_type == 'Point']
                    logging.info(f"Filtered from {point_count} to {len(combined_gdf)} point geometries")

                # Save to shapefile
                output_dir = os.path.dirname(output_path)
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                combined_gdf.to_file(output_path)

                logging.info(f"Combined {len(combined_gdf)} points from {len(detection_files)} files")
                logging.info(f"Saved combined shapefile to {output_path}")
            else:
                logging.warning("No valid points to combine")
        except Exception as e:
            logging.error(f"Error combining detection points: {str(e)}")
            raise

class TextHandler(logging.Handler):
    """Handler for redirecting logging to tkinter Text widget."""

    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.yview(tk.END)

        # This is necessary because we can't modify the Text widget from another thread
        self.text_widget.after(0, append)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description='Combine tree detection point shapefiles.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument('--division_path',
                        help='Path to division folder containing detection point files',
                        type=str)

    parser.add_argument('--boundary_path',
                        help='Path to boundary polygon shapefile',
                        type=str)

    parser.add_argument('--output_path',
                        help='Path to save the combined output shapefile',
                        type=str)

    args = parser.parse_args()

    # If arguments are provided, run in command-line mode
    if args.division_path and args.boundary_path and args.output_path:
        combiner = DetectionPointCombiner()
        combiner.combine_detection_points(args.division_path, args.boundary_path, args.output_path)
    else:
        # Otherwise, run in GUI mode
        root = tk.Tk()
        app = DetectionPointCombiner(root)
        root.mainloop()

if __name__ == "__main__":
    main()
