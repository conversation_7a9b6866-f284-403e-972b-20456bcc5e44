# Image to Shapefile Georeferencing Tools

This toolkit helps you georeference raster images to align with shapefiles and extract features from those georeferenced images to new shapefiles.

## Tools Included

1. **Map Georeferencer** (`map_georeferencer.py`) - Basic georeferencing tool for simple point-based georeferencing.
2. **Shapefile Georeferencer** (`shapefile_georeferencer.py`) - Advanced tool for aligning images with existing shapefiles.
3. **Image Feature Extractor** (`image_feature_extractor.py`) - Tool for extracting features from georeferenced images to shapefiles.

## Installation

1. Make sure you have Python 3.8+ installed
2. Install the required dependencies:

```
pip install -r requirements.txt
```

## Shapefile Georeferencer

This tool allows you to overlay a shapefile on an image and adjust its position to achieve a proper alignment. You can save the georeferencing configuration for later use with the feature extractor.

### How to use:

1. Run the script:
```
python shapefile_georeferencer.py
```

2. Load your image and shapefile using the respective buttons.
3. Use the shift controls to adjust the shapefile's position over the image.
4. Add control points to establish the correspondence between the image and shapefile coordinates:
   - Click "Add Control Point"
   - Click on a feature in the image
   - Click on the corresponding feature in the shapefile
5. Save the configuration to a JSON file.

## Image Feature Extractor

This tool allows you to extract features from a georeferenced image and save them as shapefiles.

### How to use:

1. Run the script:
```
python image_feature_extractor.py
```

2. Load the georeferencing configuration file you created with the Shapefile Georeferencer.
3. If needed, load a different image than the one specified in the config.
4. Select the feature type (polygon, line, point) and extraction method:
   - **Manual Drawing**: Draw features by clicking on the image
     - For polygons/lines: click to add points, right-click to complete
     - For points: each click adds a point feature
   - **Color Selection**: Select areas of similar color
     - Click on a color in the image
     - Adjust the threshold to control the sensitivity
     - Click "Apply Color Selection" to create polygon features from the selected areas
5. Export your features to shapefiles using the "Export to Shapefile" button.

## Workflow Example

### Aligning an image with an existing shapefile:

1. Run `shapefile_georeferencer.py`
2. Load the map image (`D. Peta Sebaran Areal HCV_page-0001.png`)
3. Load the shapefile (`shp_rebinmasjaya.shp`)
4. Adjust the position of the shapefile to align with the image
5. Add control points to improve the alignment accuracy
6. Save the configuration file

### Extracting features from the georeferenced image:

1. Run `image_feature_extractor.py`
2. Load the configuration file created previously
3. Draw or select areas of interest using the available tools
4. Export the extracted features as shapefiles

## Tips for Better Results

1. Use at least 3-4 control points distributed across different parts of the image
2. Zoom in when placing control points for better precision
3. For color selection, adjust the threshold to get cleaner boundaries
4. Filter out noisy or unwanted features before exporting
