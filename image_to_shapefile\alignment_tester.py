import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import geopandas as gpd
import rasterio
from rasterio.plot import show
from rasterio.transform import from_bounds
from rasterio.features import geometry_mask
from shapely.geometry import Point, Polygon, mapping
import cv2
from PIL import Image
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class AlignmentTester:
    def __init__(self, root=None):
        """
        Initialize the alignment tester.

        Args:
            root: Tkinter root window (if None, will create a new one)
        """
        # Initialize variables
        self.shapefile_path = None
        self.image_path = None
        self.gdf = None
        self.image = None
        self.georef_config = None
        self.transform = None

        # Create GUI if root is provided
        if root:
            self.root = root
            self.setup_gui()
        else:
            self.root = None

    def setup_gui(self):
        """Set up the GUI components."""
        self.root.title("Shapefile-Image Alignment Tester")
        self.root.geometry("1200x800")

        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # Shapefile selection
        ttk.Label(control_frame, text="Shapefile:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.shapefile_var = tk.StringVar()
        ttk.Entry(control_frame, textvariable=self.shapefile_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(control_frame, text="Browse...", command=self.browse_shapefile).grid(row=0, column=2, padx=5, pady=5)

        # Image selection
        ttk.Label(control_frame, text="Image:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.image_var = tk.StringVar()
        ttk.Entry(control_frame, textvariable=self.image_var, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(control_frame, text="Browse...", command=self.browse_image).grid(row=1, column=2, padx=5, pady=5)

        # Georef config selection
        ttk.Label(control_frame, text="Georef Config:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.config_var = tk.StringVar()
        ttk.Entry(control_frame, textvariable=self.config_var, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(control_frame, text="Browse...", command=self.browse_config).grid(row=2, column=2, padx=5, pady=5)

        # Action buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=10)

        ttk.Button(button_frame, text="Test Alignment", command=self.test_alignment).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Result", command=self.save_result).pack(side=tk.LEFT, padx=5)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Create figure for matplotlib
        self.fig = plt.Figure(figsize=(10, 8))
        self.ax = self.fig.add_subplot(111)

        # Create canvas for matplotlib figure
        self.canvas = FigureCanvasTkAgg(self.fig, master=main_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.canvas.draw()

    def browse_shapefile(self):
        """Browse for a shapefile."""
        file_path = filedialog.askopenfilename(
            title="Select Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.shapefile_var.set(file_path)
            self.shapefile_path = file_path
            self.status_var.set(f"Shapefile selected: {file_path}")

    def browse_image(self):
        """Browse for an image."""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.tif;*.tiff"), ("All files", "*.*")]
        )
        if file_path:
            self.image_var.set(file_path)
            self.image_path = file_path
            self.status_var.set(f"Image selected: {file_path}")

    def browse_config(self):
        """Browse for a georeferencing configuration file."""
        file_path = filedialog.askopenfilename(
            title="Select Georeferencing Config",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.config_var.set(file_path)
            self.load_georef_config(file_path)
            self.status_var.set(f"Georef config loaded: {file_path}")

    def load_georef_config(self, config_path):
        """Load georeferencing configuration from a JSON file."""
        try:
            with open(config_path, 'r') as f:
                self.georef_config = json.load(f)
            return True
        except Exception as e:
            if self.root:
                messagebox.showerror("Error", f"Failed to load georef config: {str(e)}")
            else:
                print(f"Error: Failed to load georef config: {str(e)}")
            return False

    def load_shapefile(self, shapefile_path=None):
        """Load a shapefile."""
        if shapefile_path:
            self.shapefile_path = shapefile_path

        if not self.shapefile_path:
            if self.root:
                messagebox.showerror("Error", "No shapefile path specified")
            else:
                print("Error: No shapefile path specified")
            return False

        try:
            self.gdf = gpd.read_file(self.shapefile_path)
            return True
        except Exception as e:
            if self.root:
                messagebox.showerror("Error", f"Failed to load shapefile: {str(e)}")
            else:
                print(f"Error: Failed to load shapefile: {str(e)}")
            return False

    def load_image(self, image_path=None):
        """Load an image."""
        if image_path:
            self.image_path = image_path

        if not self.image_path:
            if self.root:
                messagebox.showerror("Error", "No image path specified")
            else:
                print("Error: No image path specified")
            return False

        try:
            # Load image using PIL
            self.image = Image.open(self.image_path)
            self.image_array = np.array(self.image)
            return True
        except Exception as e:
            if self.root:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            else:
                print(f"Error: Failed to load image: {str(e)}")
            return False

    def create_transform(self):
        """Create a transformation matrix from the georeferencing configuration."""
        if not self.georef_config or not self.image:
            return False

        try:
            # Get image dimensions
            img_width = self.image.width
            img_height = self.image.height

            # Check if we have at least two points (minimum for accurate transformation)
            if "points" not in self.georef_config or len(self.georef_config["points"]) < 2:
                if self.root:
                    messagebox.showerror("Error", "Georef config must contain at least two points")
                else:
                    print("Error: Georef config must contain at least two points")
                return False

            # Store the points for later use
            self.control_points = []
            for point in self.georef_config["points"]:
                self.control_points.append({
                    "pixel_x": point["pixel_x"],
                    "pixel_y": point["pixel_y"],
                    "longitude": point["longitude"],
                    "latitude": point["latitude"]
                })

            # For a more accurate transformation, we'll use the full set of control points
            # to calculate the transformation parameters

            # If we have exactly 4 points in a grid pattern (common for maps),
            # we can use them directly for the transformation
            if len(self.control_points) == 4:
                # Check if points form a grid (approximately)
                # Sort points by y then x
                sorted_points = sorted(self.control_points, key=lambda p: (p["pixel_y"], p["pixel_x"]))

                # Check if we have a grid pattern
                if (abs(sorted_points[0]["pixel_y"] - sorted_points[1]["pixel_y"]) < 10 and
                    abs(sorted_points[2]["pixel_y"] - sorted_points[3]["pixel_y"]) < 10 and
                    abs(sorted_points[0]["pixel_x"] - sorted_points[2]["pixel_x"]) < 10 and
                    abs(sorted_points[1]["pixel_x"] - sorted_points[3]["pixel_x"]) < 10):

                    # We have a grid, use the corners for the transformation
                    # Reorder points to: top-left, top-right, bottom-right, bottom-left
                    tl = sorted_points[0]
                    tr = sorted_points[1]
                    bl = sorted_points[2]
                    br = sorted_points[3]

                    # Calculate bounds
                    left = min(tl["longitude"], bl["longitude"])
                    right = max(tr["longitude"], br["longitude"])
                    top = max(tl["latitude"], tr["latitude"])
                    bottom = min(bl["latitude"], br["latitude"])

                    # Store bounds for later use
                    self.bounds = (left, bottom, right, top)

                    # Create transform
                    self.transform = from_bounds(left, bottom, right, top, img_width, img_height)

                    # Print debug info
                    print(f"Using grid transformation with bounds: {left}, {bottom}, {right}, {top}")
                    return True

            # If we don't have a grid or have more/fewer than 4 points,
            # calculate the transformation using all available points

            # Calculate average scales
            scales_x = []
            scales_y = []

            for i in range(len(self.control_points)):
                for j in range(i+1, len(self.control_points)):
                    p1 = self.control_points[i]
                    p2 = self.control_points[j]

                    dx_pixels = abs(p1["pixel_x"] - p2["pixel_x"])
                    dy_pixels = abs(p1["pixel_y"] - p2["pixel_y"])

                    dx_geo = abs(p1["longitude"] - p2["longitude"])
                    dy_geo = abs(p1["latitude"] - p2["latitude"])

                    if dx_pixels > 0:
                        scales_x.append(dx_geo / dx_pixels)
                    if dy_pixels > 0:
                        scales_y.append(dy_geo / dy_pixels)

            scale_x = np.median(scales_x) if scales_x else 0.0001
            scale_y = np.median(scales_y) if scales_y else 0.0001

            # Use the first point as reference
            ref_point = self.control_points[0]
            ref_pixel_x = ref_point["pixel_x"]
            ref_pixel_y = ref_point["pixel_y"]
            ref_lon = ref_point["longitude"]
            ref_lat = ref_point["latitude"]

            # Calculate bounds
            left = ref_lon - (ref_pixel_x * scale_x)
            right = left + (img_width * scale_x)
            bottom = ref_lat + (ref_pixel_y * scale_y) - (img_height * scale_y)
            top = ref_lat + (ref_pixel_y * scale_y)

            # Store bounds for later use
            self.bounds = (left, bottom, right, top)

            # Create transform
            self.transform = from_bounds(left, bottom, right, top, img_width, img_height)

            # Print debug info
            print(f"Using general transformation with scales: {scale_x}, {scale_y}")
            print(f"Bounds: {left}, {bottom}, {right}, {top}")

            return True
        except Exception as e:
            if self.root:
                messagebox.showerror("Error", f"Failed to create transform: {str(e)}")
            else:
                print(f"Error: Failed to create transform: {str(e)}")
            return False

    def test_alignment(self):
        """Test the alignment between the shapefile and the image."""
        # Load data if not already loaded
        if not hasattr(self, 'gdf') or self.gdf is None:
            if not self.load_shapefile():
                return

        if not hasattr(self, 'image') or self.image is None:
            if not self.load_image():
                return

        if not hasattr(self, 'georef_config') or self.georef_config is None:
            if not self.load_georef_config(self.config_var.get()):
                return

        if not hasattr(self, 'transform') or self.transform is None:
            if not self.create_transform():
                return

        # Clear the plot
        self.ax.clear()

        # Display the image
        self.ax.imshow(self.image_array)

        # Convert shapefile coordinates to pixel coordinates
        pixel_gdf = self.convert_shapefile_to_pixels()

        if pixel_gdf is not None:
            try:
                # Plot the shapefile manually to avoid aspect ratio issues
                for idx, row in pixel_gdf.iterrows():
                    if row.geometry.geom_type == 'Polygon':
                        x, y = row.geometry.exterior.xy
                        self.ax.plot(x, y, color='red', linewidth=2)
                    elif row.geometry.geom_type == 'MultiPolygon':
                        for geom in row.geometry.geoms:
                            x, y = geom.exterior.xy
                            self.ax.plot(x, y, color='red', linewidth=2)

                # Add control points for reference
                if hasattr(self, 'control_points'):
                    for i, point in enumerate(self.control_points):
                        self.ax.plot(point["pixel_x"], point["pixel_y"], 'o', color='blue', markersize=8)
                        self.ax.text(point["pixel_x"] + 10, point["pixel_y"] + 10,
                                    f"CP{i+1}: ({point['latitude']:.4f}, {point['longitude']:.4f})",
                                    color='white', fontsize=8, backgroundcolor='black')

                # Add title
                self.ax.set_title("Shapefile Overlay on Image")

                # Create custom legend
                from matplotlib.lines import Line2D
                legend_elements = [
                    Line2D([0], [0], color='red', lw=2, label='Shapefile Boundary'),
                    Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='Control Points')
                ]
                self.ax.legend(handles=legend_elements, loc='upper right')

                # Remove axes for cleaner look
                self.ax.set_axis_off()
            except Exception as e:
                print(f"Error plotting shapefile: {str(e)}")
                import traceback
                traceback.print_exc()

            # Update status
            self.status_var.set("Alignment test completed. Red lines show shapefile boundaries.")
        else:
            self.status_var.set("Failed to convert shapefile to pixel coordinates.")

        # Refresh the canvas
        self.canvas.draw()

    def convert_shapefile_to_pixels(self):
        """Convert shapefile coordinates to pixel coordinates."""
        try:
            # Create a copy of the GeoDataFrame
            pixel_gdf = self.gdf.copy()

            # Get image dimensions
            img_width = self.image.width
            img_height = self.image.height

            # Check if the shapefile has a CRS
            if pixel_gdf.crs is None:
                print("Warning: Shapefile has no CRS. Assuming EPSG:4326 (WGS84).")
                pixel_gdf.set_crs(epsg=4326, inplace=True)
            elif pixel_gdf.crs.to_epsg() != 4326:
                print(f"Converting shapefile from {pixel_gdf.crs} to EPSG:4326")
                pixel_gdf = pixel_gdf.to_crs(epsg=4326)

            # Function to convert geo coordinates to pixel coordinates
            def geo_to_pixel(geom):
                from shapely.ops import transform

                def transformer(x, y):
                    # Manual transformation based on control points
                    # This is a more direct approach that should fix the orientation issue

                    # Get bounds from the transform
                    left, bottom, right, top = self.bounds

                    # Calculate the position relative to the bounds
                    x_ratio = (x - left) / (right - left)
                    y_ratio = (y - bottom) / (top - bottom)

                    # Convert to pixel coordinates
                    # Note: For y, we invert because image coordinates start at the top
                    pixel_x = x_ratio * img_width
                    pixel_y = (1 - y_ratio) * img_height

                    return pixel_x, pixel_y

                return transform(transformer, geom)

            # Apply the transformation to each geometry
            pixel_gdf.geometry = pixel_gdf.geometry.apply(geo_to_pixel)

            # Print some debug info
            print(f"Shapefile bounds (original): {self.gdf.total_bounds}")
            print(f"Shapefile bounds (pixel): {pixel_gdf.total_bounds}")
            print(f"Image dimensions: {img_width} x {img_height}")

            return pixel_gdf
        except Exception as e:
            if self.root:
                messagebox.showerror("Error", f"Failed to convert shapefile: {str(e)}")
            else:
                print(f"Error: Failed to convert shapefile: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def save_result(self):
        """Save the alignment test result."""
        if not hasattr(self, 'fig'):
            if self.root:
                messagebox.showerror("Error", "No result to save")
            else:
                print("Error: No result to save")
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Result",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
                self.status_var.set(f"Result saved to {file_path}")
                if self.root:
                    messagebox.showinfo("Success", f"Result saved to {file_path}")
            except Exception as e:
                if self.root:
                    messagebox.showerror("Error", f"Failed to save result: {str(e)}")
                else:
                    print(f"Error: Failed to save result: {str(e)}")

def main():
    """Main function to run the application."""
    root = tk.Tk()
    app = AlignmentTester(root)

    # Set default values if provided as command-line arguments
    if len(sys.argv) > 1:
        app.shapefile_var.set(sys.argv[1])
        app.shapefile_path = sys.argv[1]

    if len(sys.argv) > 2:
        app.image_var.set(sys.argv[2])
        app.image_path = sys.argv[2]

    if len(sys.argv) > 3:
        app.config_var.set(sys.argv[3])
        app.load_georef_config(sys.argv[3])

    root.mainloop()

if __name__ == "__main__":
    main()
