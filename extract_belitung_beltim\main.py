import geopandas as gpd
from shapely.geometry import LineString
import uuid
import os

# Path ke file shapefile
input_shp = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\batas_desa_belitung\batas_desa_belitung.shp"

# Baca file shapefile
gdf = gpd.read_file(input_shp)

# Filter wilayah Belitung dan Belitung Timur berdasarkan WADMKK
belitung = gdf[gdf['WADMKK'] == 'Belitung']
belitung_timur = gdf[gdf['WADMKK'] == 'Belitung Timur']

# Inisialisasi list untuk menyimpan geometri garis batas
boundary_lines = []

# Iterasi untuk menemukan batas yang berhimpitan
for idx1, poly1 in belitung.iterrows():
    for idx2, poly2 in belitung_timur.iterrows():
        # Cek jika poligon berpotongan
        if poly1.geometry.intersects(poly2.geometry):
            # Dapatkan garis batas dari intersection
            intersection = poly1.geometry.intersection(poly2.geometry)
            # Pastikan hasilnya adalah LineString atau MultiLineString
            if intersection.geom_type in ['LineString', 'MultiLineString']:
                if intersection.geom_type == 'LineString':
                    boundary_lines.append(intersection)
                else:
                    # Untuk MultiLineString, tambahkan setiap LineString
                    for line in intersection.geoms:
                        boundary_lines.append(line)

# Jika tidak ada batas yang ditemukan
if not boundary_lines:
    print("Tidak ditemukan batas yang berhimpitan antara Belitung dan Belitung Timur.")
else:
    # Buat GeoDataFrame untuk garis batas
    boundary_gdf = gpd.GeoDataFrame(geometry=boundary_lines, crs=gdf.crs)
    
    # Tambahkan kolom atribut
    boundary_gdf['boundary_id'] = [str(uuid.uuid4()) for _ in range(len(boundary_gdf))]
    boundary_gdf['WADMKK_1'] = 'Belitung'
    boundary_gdf['WADMKK_2'] = 'Belitung Timur'
    
    # Path untuk menyimpan file output di direktori yang sama
    output_dir = os.path.dirname(input_shp)
    output_file = os.path.join(output_dir, 'boundary_belitung_belitung_timur.shp')
    
    # Simpan ke file shapefile baru
    boundary_gdf.to_file(output_file)
    print(f"Garis batas telah disimpan ke {output_file}")