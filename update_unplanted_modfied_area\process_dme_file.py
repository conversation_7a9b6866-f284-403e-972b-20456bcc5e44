#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Process DME Shapefile

This script specifically processes the DME shapefile with inclaves to fix the issue
with SUB DIVISI AIR KANDIS and BLOK P 07/04.

Author: AI Assistant
Date: 2025-05-20
"""

import os
import sys
import logging
from update_inclave_areas import InclaveAreaUpdater, logger

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def main():
    """Process the DME shapefile."""
    # Set the input file path
    input_file = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Dme\ddd_with_inclave_8m.shp"
    
    # Create the output file path
    dirname = os.path.dirname(input_file)
    basename = os.path.splitext(os.path.basename(input_file))[0]
    output_file = os.path.join(dirname, f"{basename}_updated.shp")
    
    # Check if the input file exists
    if not os.path.exists(input_file):
        logger.error(f"Input file not found: {input_file}")
        print(f"Error: Input file not found: {input_file}")
        return False
    
    # Create the updater and process the shapefile
    logger.info(f"Processing file: {input_file}")
    print(f"Processing file: {input_file}")
    
    updater = InclaveAreaUpdater(input_file)
    
    if updater.load_shapefile():
        logger.info("Shapefile loaded successfully")
        print("Shapefile loaded successfully")
        
        if updater.update_areas():
            logger.info("Areas updated successfully")
            print("Areas updated successfully")
            
            # Generate a report
            updater.generate_report()
            
            # Save the updated shapefile
            if updater.save_updated_shapefile(output_file):
                logger.info(f"Updated shapefile saved to: {output_file}")
                print(f"Updated shapefile saved to: {output_file}")
                
                # Generate a visualization
                viz_output = os.path.splitext(output_file)[0] + "_visualization.png"
                updater.visualize_changes(viz_output)
                
                return True
            else:
                logger.error("Failed to save updated shapefile")
                print("Error: Failed to save updated shapefile")
        else:
            logger.error("Failed to update areas")
            print("Error: Failed to update areas")
    else:
        logger.error("Failed to load shapefile")
        print("Error: Failed to load shapefile")
    
    return False

if __name__ == "__main__":
    success = main()
    if success:
        print("Processing completed successfully!")
    else:
        print("Processing failed. Check the log for details.")
