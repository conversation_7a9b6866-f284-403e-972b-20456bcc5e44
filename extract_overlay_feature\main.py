import geopandas as gpd

# Path ke shapefile (gunakan path dari proyek sebelumnya)
shp_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\hgu detail\hgu detail\SHP_DETAIL_HGU_2025.shp"

# Baca shapefile
gdf = gpd.read_file(shp_path)

# Pastikan kolom "Nomor" ada
if "Nomor" not in gdf.columns:
    print("Kolom 'Nomor' tidak ditemukan di shapefile!")
else:
    # Filter fitur dengan Nomor "HGU 3 / 2006" dan "HGU 09 /1999"
    hgu_3_2006 = gdf[gdf["Nomor"] == "HGU 3 / 2006"]
    hgu_09_1999 = gdf[gdf["Nomor"] == "HGU 09 /1999"]

    # Pastikan kedua fitur ditemukan
    if hgu_3_2006.empty or hgu_09_1999.empty:
        print("Salah satu atau kedua fitur tidak ditemukan! Periksa nilai di kolom 'Nomor'.")
    else:
        # Lakukan intersection untuk mendapatkan area tumpang tindih
        overlap = gpd.overlay(hgu_3_2006, hgu_09_1999, how="intersection")

        # Jika ada area tumpang tindih, simpan ke file baru
        if not overlap.empty:
            output_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\hgu detail\hgu detail\overlay_HGU_3_9.shp"
            overlap.to_file(output_path)
            print(f"Area tumpang tindih berhasil disimpan di: {output_path}")
        else:
            print("Tidak ada area tumpang tindih antara kedua fitur!")