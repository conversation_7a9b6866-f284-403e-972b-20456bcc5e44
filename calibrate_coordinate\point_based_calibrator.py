import geopandas as gpd
import numpy as np
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from shapely.geometry import Point, Polygon, LineString, MultiPolygon, MultiLineString
import pandas as pd
from shapely.affinity import translate
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PointBasedCalibrator:
    def __init__(self, master):
        self.master = master
        self.master.title("Point-Based Shapefile Calibrator")
        self.master.geometry("1200x850")
        
        # Initialize variables
        self.reference_shapefile = tk.StringVar()
        self.movable_shapefile = tk.StringVar()
        self.output_shapefile = tk.StringVar()
        
        # Default paths
        default_reference = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\HGU REBINMAS\shp_rebinmasjaya.shp"
        default_movable = r"D:\Gawean Rebinmas\Geo  Processing GIS Alat\image_to_shapefile\green_areas.shp"
        
        if os.path.exists(default_reference):
            self.reference_shapefile.set(default_reference)
        
        if os.path.exists(default_movable):
            self.movable_shapefile.set(default_movable)
            # Set default output path
            output_dir = os.path.dirname(default_movable)
            self.output_shapefile.set(os.path.join(output_dir, "calibrated_green_areas.shp"))
        
        # Initialize data
        self.reference_gdf = None
        self.movable_gdf = None
        self.transformed_gdf = None
        
        # Point data
        self.current_points = []  # List to store current points (x, y)
        self.target_points = []   # List to store target points (x, y)
        
        # Create GUI
        self.create_gui()
        
        # Create matplotlib figure for visualization
        self.create_plot()
        
    def create_gui(self):
        # Create main frame
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Split into left control panel and right plot
        left_panel = ttk.Frame(main_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # File selection
        file_frame = ttk.LabelFrame(left_panel, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(file_frame, text="Reference Shapefile:").pack(anchor=tk.W)
        ref_entry = ttk.Entry(file_frame, textvariable=self.reference_shapefile, width=40)
        ref_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="Browse...", command=lambda: self.browse_file(self.reference_shapefile)).pack(anchor=tk.W)
        
        ttk.Label(file_frame, text="Shapefile to Calibrate:").pack(anchor=tk.W, pady=(10, 0))
        mov_entry = ttk.Entry(file_frame, textvariable=self.movable_shapefile, width=40)
        mov_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="Browse...", command=lambda: self.browse_file(self.movable_shapefile)).pack(anchor=tk.W)
        
        ttk.Label(file_frame, text="Output Shapefile:").pack(anchor=tk.W, pady=(10, 0))
        out_entry = ttk.Entry(file_frame, textvariable=self.output_shapefile, width=40)
        out_entry.pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="Browse...", command=lambda: self.browse_file_save(self.output_shapefile)).pack(anchor=tk.W)
        
        # Load button
        ttk.Button(file_frame, text="Load Shapefiles", command=self.load_shapefiles).pack(fill=tk.X, pady=(10, 0))
        
        # Reference Points frame
        points_frame = ttk.LabelFrame(left_panel, text="Reference Points", padding="10")
        points_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create a frame with two columns for current and target points
        points_container = ttk.Frame(points_frame)
        points_container.pack(fill=tk.X, pady=(5, 0))
        
        # Current points column
        current_frame = ttk.LabelFrame(points_container, text="Current Point (Source)")
        current_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Label(current_frame, text="X:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.current_x = ttk.Entry(current_frame, width=15)
        self.current_x.grid(row=0, column=1, padx=5, pady=2)
        self.current_x.insert(0, "824479.733")
        
        ttk.Label(current_frame, text="Y:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.current_y = ttk.Entry(current_frame, width=15)
        self.current_y.grid(row=1, column=1, padx=5, pady=2)
        self.current_y.insert(0, "9684076.776")
        
        # Target points column
        target_frame = ttk.LabelFrame(points_container, text="Target Point (Destination)")
        target_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Label(target_frame, text="X:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.target_x = ttk.Entry(target_frame, width=15)
        self.target_x.grid(row=0, column=1, padx=5, pady=2)
        self.target_x.insert(0, "824105.400")
        
        ttk.Label(target_frame, text="Y:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.target_y = ttk.Entry(target_frame, width=15)
        self.target_y.grid(row=1, column=1, padx=5, pady=2)
        self.target_y.insert(0, "9684031.784")
        
        # Button to calculate shift
        ttk.Button(points_frame, text="Calculate Shift", command=self.calculate_shift).pack(fill=tk.X, pady=(10, 0))
        
        # Shift information frame
        shift_frame = ttk.LabelFrame(left_panel, text="Calculated Shift", padding="10")
        shift_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Horizontal shift
        ttk.Label(shift_frame, text="Horizontal Shift (X):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.h_shift_value = ttk.Label(shift_frame, text="0.0")
        self.h_shift_value.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        self.h_shift_info = ttk.Label(shift_frame, text="(+ means right, - means left)")
        self.h_shift_info.grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        
        # Vertical shift
        ttk.Label(shift_frame, text="Vertical Shift (Y):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.v_shift_value = ttk.Label(shift_frame, text="0.0")
        self.v_shift_value.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        self.v_shift_info = ttk.Label(shift_frame, text="(+ means up, - means down)")
        self.v_shift_info.grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        
        # Total distance
        ttk.Label(shift_frame, text="Total Distance:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.distance_value = ttk.Label(shift_frame, text="0.0")
        self.distance_value.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        self.distance_unit = ttk.Label(shift_frame, text="meters")
        self.distance_unit.grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)
        
        # Apply and Save frame
        action_frame = ttk.LabelFrame(left_panel, text="Actions", padding="10")
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(action_frame, text="Apply Shift", command=self.apply_shift).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(action_frame, text="Save Calibrated Shapefile", command=self.save_shapefile).pack(fill=tk.X)
        ttk.Button(action_frame, text="View Transformation Details", command=self.show_transformation_details).pack(fill=tk.X, pady=(5, 0))
        
        # Feature info frame
        info_frame = ttk.LabelFrame(left_panel, text="Feature Information", padding="10")
        info_frame.pack(fill=tk.X)
        
        ttk.Label(info_frame, text="Number of Features:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.feature_count = ttk.Label(info_frame, text="0")
        self.feature_count.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(info_frame, text="Geometry Types:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.geometry_types = ttk.Label(info_frame, text="-")
        self.geometry_types.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Select application method
        method_frame = ttk.LabelFrame(left_panel, text="Transformation Method", padding="10")
        method_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.transform_method = tk.StringVar(value="matrix")
        ttk.Radiobutton(method_frame, text="Matrix Transformation (recommended for consistency)", 
                      variable=self.transform_method, value="matrix").pack(anchor=tk.W)
        ttk.Radiobutton(method_frame, text="Shapely Affine (traditional method)", 
                      variable=self.transform_method, value="shapely").pack(anchor=tk.W)
        
    def create_plot(self):
        # Create frame for plot
        plot_frame = ttk.LabelFrame(self.master, text="Visualization", padding="10")
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Create matplotlib figure and canvas
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar_frame = ttk.Frame(plot_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        
        # Initialize empty plot
        self.ax.set_title("Shapefile Calibration")
        self.ax.set_xlabel("Longitude")
        self.ax.set_ylabel("Latitude")
        self.ax.grid(True)
        
        self.canvas.draw()
    
    def show_transformation_details(self):
        if self.movable_gdf is None:
            messagebox.showerror("Error", "Please load shapefiles first")
            return
            
        # Create new window for details
        details_window = tk.Toplevel(self.master)
        details_window.title("Transformation Details")
        details_window.geometry("800x600")
        
        # Create frame with scrollable text
        frame = ttk.Frame(details_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Create scrolled text widget
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True)
        
        # Add header
        text_widget.insert(tk.END, "SHAPEFILE TRANSFORMATION DETAILS\n")
        text_widget.insert(tk.END, "-" * 50 + "\n\n")
        
        # Add general info
        text_widget.insert(tk.END, f"Source Shapefile: {self.movable_shapefile.get()}\n")
        text_widget.insert(tk.END, f"Total Features: {len(self.movable_gdf)}\n")
        
        # Count by geometry type
        geom_types = self.movable_gdf.geometry.type.value_counts().to_dict()
        text_widget.insert(tk.END, "Geometry Types:\n")
        for gtype, count in geom_types.items():
            text_widget.insert(tk.END, f"  - {gtype}: {count}\n")
        
        text_widget.insert(tk.END, "\n")
        
        # Transformation parameters
        x_shift = float(self.h_shift_value.cget("text") or 0)
        y_shift = float(self.v_shift_value.cget("text") or 0)
        text_widget.insert(tk.END, f"Horizontal Shift (X): {x_shift}\n")
        text_widget.insert(tk.END, f"Vertical Shift (Y): {y_shift}\n")
        text_widget.insert(tk.END, f"Total Distance: {float(self.distance_value.cget('text') or 0)}\n\n")
        
        # Sample of geometries before and after
        if self.transformed_gdf is not None and not self.transformed_gdf.equals(self.movable_gdf):
            text_widget.insert(tk.END, "SAMPLE GEOMETRY TRANSFORMATIONS\n")
            text_widget.insert(tk.END, "-" * 50 + "\n\n")
            
            # Take up to 5 samples of each geometry type
            for gtype in geom_types.keys():
                samples = self.movable_gdf[self.movable_gdf.geometry.type == gtype].head(2)
                if not samples.empty:
                    text_widget.insert(tk.END, f"Sample {gtype} Transformations:\n")
                    for idx, row in samples.iterrows():
                        before = row.geometry
                        after = self.transformed_gdf.loc[idx, 'geometry']
                        
                        # Get bounding boxes for comparison
                        before_bounds = before.bounds
                        after_bounds = after.bounds
                        
                        # Calculate center points
                        before_center = ((before_bounds[0] + before_bounds[2])/2, 
                                        (before_bounds[1] + before_bounds[3])/2)
                        after_center = ((after_bounds[0] + after_bounds[2])/2, 
                                       (after_bounds[1] + after_bounds[3])/2)
                        
                        # Calculate actual shift
                        actual_x_shift = after_center[0] - before_center[0]
                        actual_y_shift = after_center[1] - before_center[1]
                        
                        # Report
                        text_widget.insert(tk.END, f"  Feature {idx}:\n")
                        text_widget.insert(tk.END, f"    Before Center: ({before_center[0]:.3f}, {before_center[1]:.3f})\n")
                        text_widget.insert(tk.END, f"    After Center: ({after_center[0]:.3f}, {after_center[1]:.3f})\n")
                        text_widget.insert(tk.END, f"    Actual X Shift: {actual_x_shift:.3f} (Expected: {x_shift})\n")
                        text_widget.insert(tk.END, f"    Actual Y Shift: {actual_y_shift:.3f} (Expected: {y_shift})\n")
                        text_widget.insert(tk.END, f"    Difference: X={abs(actual_x_shift-x_shift):.6f}, Y={abs(actual_y_shift-y_shift):.6f}\n\n")
                    
                    text_widget.insert(tk.END, "\n")
        
        # Make text widget read-only
        text_widget.config(state=tk.DISABLED)
        
    def browse_file(self, string_var):
        filename = filedialog.askopenfilename(
            title="Select Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            string_var.set(filename)
    
    def browse_file_save(self, string_var):
        filename = filedialog.asksaveasfilename(
            title="Save Shapefile As",
            defaultextension=".shp",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            string_var.set(filename)
    
    def load_shapefiles(self):
        reference_path = self.reference_shapefile.get()
        movable_path = self.movable_shapefile.get()
        
        if not os.path.exists(reference_path):
            messagebox.showerror("Error", f"Reference shapefile not found: {reference_path}")
            return
        
        if not os.path.exists(movable_path):
            messagebox.showerror("Error", f"Movable shapefile not found: {movable_path}")
            return
        
        try:
            self.reference_gdf = gpd.read_file(reference_path)
            self.movable_gdf = gpd.read_file(movable_path)
            
            # Ensure both are in the same CRS
            if self.reference_gdf.crs != self.movable_gdf.crs:
                self.movable_gdf = self.movable_gdf.to_crs(self.reference_gdf.crs)
                messagebox.showinfo("CRS Conversion", 
                                   f"Converted movable shapefile from {self.movable_gdf.crs} to {self.reference_gdf.crs}")
            
            # Create a copy for transformation
            self.transformed_gdf = self.movable_gdf.copy()
            
            # Update feature info
            self.update_feature_info()
            
            # Update plot
            self.update_plot()
            messagebox.showinfo("Success", "Shapefiles loaded successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load shapefiles: {str(e)}")
            logger.error(f"Failed to load shapefiles: {str(e)}", exc_info=True)
    
    def update_feature_info(self):
        if self.movable_gdf is not None:
            # Update feature count
            self.feature_count.config(text=str(len(self.movable_gdf)))
            
            # Update geometry types
            geom_types = self.movable_gdf.geometry.type.value_counts().to_dict()
            geom_type_str = ", ".join([f"{k}: {v}" for k, v in geom_types.items()])
            self.geometry_types.config(text=geom_type_str)
    
    def calculate_shift(self):
        try:
            # Get current point coordinates
            current_x = float(self.current_x.get())
            current_y = float(self.current_y.get())
            
            # Get target point coordinates
            target_x = float(self.target_x.get())
            target_y = float(self.target_y.get())
            
            # Calculate shifts
            x_shift = target_x - current_x
            y_shift = target_y - current_y
            
            # Calculate total distance
            distance = np.sqrt(x_shift**2 + y_shift**2)
            
            # Update shift information
            self.h_shift_value.config(text=f"{x_shift:.3f}")
            self.v_shift_value.config(text=f"{y_shift:.3f}")
            self.distance_value.config(text=f"{distance:.3f}")
            
            # Store points
            self.current_points = [(current_x, current_y)]
            self.target_points = [(target_x, target_y)]
            
            # Update plot with points
            self.update_plot_with_points()
            
            messagebox.showinfo("Shift Calculation", 
                               f"Calculated shift: X = {x_shift:.3f}, Y = {y_shift:.3f}\nTotal distance: {distance:.3f} meters")
        except ValueError:
            messagebox.showerror("Input Error", "Please enter valid coordinate values")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to calculate shift: {str(e)}")
            logger.error(f"Failed to calculate shift: {str(e)}", exc_info=True)
    
    def apply_shift(self):
        if self.movable_gdf is None:
            messagebox.showerror("Error", "Please load shapefiles first")
            return
        
        try:
            # Get shift values
            x_shift = float(self.h_shift_value.cget("text"))
            y_shift = float(self.v_shift_value.cget("text"))
            
            if x_shift == 0 and y_shift == 0:
                messagebox.showwarning("Warning", "No shift to apply. Please calculate a shift first.")
                return
            
            # Create a copy of the original
            self.transformed_gdf = self.movable_gdf.copy()
            
            # Use chosen transformation method
            if self.transform_method.get() == "matrix":
                self._apply_matrix_transformation(x_shift, y_shift)
            else:
                self._apply_shapely_transformation(x_shift, y_shift)
            
            # Update plot
            self.update_plot()
            messagebox.showinfo("Success", f"Applied shift: X = {x_shift:.3f}, Y = {y_shift:.3f}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply shift: {str(e)}")
            logger.error(f"Failed to apply shift: {str(e)}", exc_info=True)
    
    def _apply_matrix_transformation(self, x_shift, y_shift):
        """Apply transformation using a consistent coordinate transformation matrix"""
        logger.info(f"Applying matrix transformation with shift X={x_shift}, Y={y_shift}")
        
        # Function to transform a set of coordinates
        def transform_coords(coords):
            if isinstance(coords, list):
                coords = np.array(coords)
                
            # Apply shift
            transformed = coords.copy()
            transformed[:, 0] += x_shift  # X coordinates
            transformed[:, 1] += y_shift  # Y coordinates
            
            return transformed
        
        # Function to process geometries recursively
        def process_geometry(geom):
            geom_type = geom.geom_type
            
            if geom_type == 'Point':
                x, y = geom.x, geom.y
                new_x, new_y = x + x_shift, y + y_shift
                return Point(new_x, new_y)
                
            elif geom_type == 'LineString':
                coords = np.array(geom.coords)
                new_coords = transform_coords(coords)
                return LineString(new_coords)
                
            elif geom_type == 'Polygon':
                # Transform exterior
                exterior_coords = np.array(geom.exterior.coords)
                new_exterior = transform_coords(exterior_coords)
                
                # Transform interiors (holes)
                new_interiors = []
                for interior in geom.interiors:
                    interior_coords = np.array(interior.coords)
                    new_interior = transform_coords(interior_coords)
                    new_interiors.append(new_interior)
                
                return Polygon(new_exterior, new_interiors)
                
            elif geom_type.startswith('Multi'):
                # Handle multi geometries
                transformed_parts = [process_geometry(part) for part in geom.geoms]
                
                if geom_type == 'MultiPoint':
                    from shapely.geometry import MultiPoint
                    return MultiPoint(transformed_parts)
                elif geom_type == 'MultiLineString':
                    from shapely.geometry import MultiLineString
                    return MultiLineString(transformed_parts)
                elif geom_type == 'MultiPolygon':
                    from shapely.geometry import MultiPolygon
                    return MultiPolygon(transformed_parts)
            
            # If not handled above, try using shapely's translate as fallback
            logger.warning(f"Using fallback translation for geometry type: {geom_type}")
            return translate(geom, xoff=x_shift, yoff=y_shift)
        
        # Apply transformation to each geometry
        try:
            # Process each feature
            for idx, row in self.transformed_gdf.iterrows():
                original_geom = row.geometry
                
                # Skip empty geometries
                if original_geom is None or original_geom.is_empty:
                    logger.warning(f"Skipping empty geometry at index {idx}")
                    continue
                
                # Transform the geometry
                try:
                    transformed_geom = process_geometry(original_geom)
                    self.transformed_gdf.at[idx, 'geometry'] = transformed_geom
                except Exception as e:
                    logger.error(f"Error transforming geometry at index {idx}: {str(e)}")
                    # Continue with the next geometry
            
        except Exception as e:
            logger.error(f"Error in matrix transformation: {str(e)}", exc_info=True)
            raise
    
    def _apply_shapely_transformation(self, x_shift, y_shift):
        """Apply transformation using shapely's affine transformation"""
        logger.info(f"Applying shapely transformation with shift X={x_shift}, Y={y_shift}")
        
        try:
            # Apply shift to each geometry
            self.transformed_gdf['geometry'] = self.transformed_gdf['geometry'].apply(
                lambda geom: translate(geom, xoff=x_shift, yoff=y_shift)
            )
        except Exception as e:
            logger.error(f"Error in shapely transformation: {str(e)}", exc_info=True)
            raise
    
    def save_shapefile(self):
        if self.transformed_gdf is None:
            messagebox.showerror("Error", "Please apply shift first")
            return
        
        output_path = self.output_shapefile.get()
        if not output_path:
            messagebox.showerror("Error", "Please specify an output file path")
            return
        
        try:
            # Save the transformed shapefile
            self.transformed_gdf.to_file(output_path)
            messagebox.showinfo("Success", f"Calibrated shapefile saved to: {output_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save shapefile: {str(e)}")
            logger.error(f"Failed to save shapefile: {str(e)}", exc_info=True)
    
    def update_plot(self):
        # Clear previous plot
        self.ax.clear()
        
        if self.reference_gdf is not None:
            # Plot reference shapefile in blue
            self.reference_gdf.plot(ax=self.ax, color='blue', alpha=0.5, label='Reference')
        
        if self.movable_gdf is not None:
            # Plot original movable shapefile in red (dashed)
            self.movable_gdf.boundary.plot(ax=self.ax, color='red', linestyle='--', alpha=0.5, label='Original')
        
        if self.transformed_gdf is not None and not self.transformed_gdf.equals(self.movable_gdf):
            # Plot transformed shapefile in green
            self.transformed_gdf.plot(ax=self.ax, color='green', alpha=0.5, label='Transformed')
        
        # Set plot properties
        self.ax.set_title("Shapefile Calibration")
        self.ax.set_xlabel("Longitude")
        self.ax.set_ylabel("Latitude")
        self.ax.grid(True)
        self.ax.legend()
        
        # Auto-adjust view to show all data
        if self.reference_gdf is not None or self.transformed_gdf is not None:
            self.ax.autoscale()
        
        self.canvas.draw()
    
    def update_plot_with_points(self):
        # Update plot with points
        self.update_plot()
        
        # Add current points in red
        for x, y in self.current_points:
            self.ax.plot(x, y, 'ro', markersize=10, label='Current Point' if self.current_points.index((x, y)) == 0 else "")
        
        # Add target points in green
        for x, y in self.target_points:
            self.ax.plot(x, y, 'go', markersize=10, label='Target Point' if self.target_points.index((x, y)) == 0 else "")
        
        # Connect points with a line
        for i in range(len(self.current_points)):
            if i < len(self.target_points):
                self.ax.plot([self.current_points[i][0], self.target_points[i][0]], 
                            [self.current_points[i][1], self.target_points[i][1]], 
                            'k--', alpha=0.7)
        
        # Update legend
        handles, labels = self.ax.get_legend_handles_labels()
        unique_labels = []
        unique_handles = []
        for handle, label in zip(handles, labels):
            if label not in unique_labels:
                unique_labels.append(label)
                unique_handles.append(handle)
        
        self.ax.legend(unique_handles, unique_labels)
        
        # Draw the plot
        self.canvas.draw()

if __name__ == "__main__":
    # Create Tkinter root window
    root = tk.Tk()
    
    # Create the application
    app = PointBasedCalibrator(root)
    
    # Start the GUI event loop
    root.mainloop() 