import geopandas as gpd

# Path ke shapefile
shp_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\hgu detail\hgu detail\SHP_DETAIL_HGU_2025.shp"

# Baca shapefile
gdf = gpd.read_file(shp_path)

# Pastikan kolom "nomor" ada
if "Nomor" not in gdf.columns:
    print("Kolom 'nomor' tidak ditemukan di shapefile!")
else:
    # Ekstrak 4 angka terakhir dari kolom "nomor" sebagai tahun
    gdf["tahun"] = gdf["Nomor"].str[-4:]

    # Simpan kembali ke shapefile (overwrite)
    gdf.to_file(shp_path)

    print("Kolom 'tahun' berhasil ditambahkan dan shapefile diperbarui!")