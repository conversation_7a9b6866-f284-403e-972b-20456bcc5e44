2025-06-12 13:05:51,692 - INFO - Generating summary report...
2025-06-12 13:05:51,696 - INFO - Column mapping applied. Available columns: ['Id', 'Divisi', 'SUB_DIVISI', 'BLOK', 'LUAS_AUTO', '<PERSON><PERSON><PERSON>_Poh', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained', 'total_incl', 'luas_netto']
2025-06-12 13:05:51,698 - INFO - Filtering for Boundary records with HCV_Catego = 0
2025-06-12 13:05:51,710 - INFO - Filtered 4 Boundary records with HCV_Catego = 0 from 8 total records
2025-06-12 13:05:51,712 - INFO - Calculating inclave areas...
2025-06-12 13:05:51,714 - INFO - Found 4 inclave records
2025-06-12 13:05:51,723 - INFO - Calculated inclave areas for 4 boundary groups
2025-06-12 13:05:51,759 - INFO - Generated summary report with 4 records
2025-06-12 13:05:51,779 - INFO - Exporting report to: test_sph_report.xlsx
2025-06-12 13:05:55,222 - INFO - Column mapping applied. Available columns: ['Id', 'Divisi', 'SUB_DIVISI', 'BLOK', 'LUAS_AUTO', 'Jumlah_Poh', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained', 'total_incl', 'luas_netto']
2025-06-12 13:05:55,223 - INFO - Filtering for Boundary records with HCV_Catego = 0
2025-06-12 13:05:55,224 - INFO - Filtered 4 Boundary records with HCV_Catego = 0 from 8 total records
2025-06-12 13:05:55,379 - INFO - Report successfully exported to: test_sph_report.xlsx
