import tkinter as tk
from tkinter import filedialog, messagebox
import geopandas as gpd

def browse_input_file():
    file_path = filedialog.askopenfilename(filetypes=[("Shapefile", "*.shp")])
    input_entry.delete(0, tk.END)
    input_entry.insert(0, file_path)

def browse_output_file():
    file_path = filedialog.asksaveasfilename(defaultextension=".shp", filetypes=[("Shapefile", "*.shp")])
    output_entry.delete(0, tk.END)
    output_entry.insert(0, file_path)

def extract_features():
    input_path = input_entry.get()
    output_path = output_entry.get()

    if not input_path or not output_path:
        messagebox.showerror("Error", "Silakan pilih file input dan output!")
        return

    try:
        # Baca file shapefile
        gdf = gpd.read_file(input_path)
        # Filter data berdasarkan HCV_Catego = 1
        filtered_gdf = gdf[gdf['HCV_Catego'] == 1]
        # Simpan hasil ke file output
        filtered_gdf.to_file(output_path)
        messagebox.showinfo("Sukses", f"Ekstraksi selesai! File disimpan di: {output_path}")
    except Exception as e:
        messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")

# Buat jendela GUI
root = tk.Tk()
root.title("Ekstrak Fitur Shapefile")
root.geometry("400x200")

# Label dan Entry untuk file input
tk.Label(root, text="Pilih File Input (.shp):").pack(pady=5)
input_frame = tk.Frame(root)
input_entry = tk.Entry(input_frame, width=30)
input_entry.pack(side=tk.LEFT, padx=5)
tk.Button(input_frame, text="Browse", command=browse_input_file).pack(side=tk.LEFT)
input_frame.pack()

# Label dan Entry untuk file output
tk.Label(root, text="Pilih Lokasi & Nama File Output (.shp):").pack(pady=5)
output_frame = tk.Frame(root)
output_entry = tk.Entry(output_frame, width=30)
output_entry.pack(side=tk.LEFT, padx=5)3
tk.Button(output_frame, text="Browse", command=browse_output_file).pack(side=tk.LEFT)
output_frame.pack()

# Tombol untuk memproses ekstraksi
tk.Button(root, text="Ekstrak Fitur (HCV_Catego = 1)", command=extract_features).pack(pady=20)

# Jalankan aplikasi
root.mainloop()